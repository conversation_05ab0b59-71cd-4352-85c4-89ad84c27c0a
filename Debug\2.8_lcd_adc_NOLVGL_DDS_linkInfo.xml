<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\bin\tiarmlnk -IG:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib -o 2.8_lcd_adc_NOLVGL_DDS.out -m2.8_lcd_adc_NOLVGL_DDS.map -iG:/ti/SDK/mspm0-sdk-main/source -iC:/Users/<USER>/workspace_ccstheia/2.8_lcd_adc_NOLVGL_DDS -iC:/Users/<USER>/workspace_ccstheia/2.8_lcd_adc_NOLVGL_DDS/Debug/syscfg -iG:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=2.8_lcd_adc_NOLVGL_DDS_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./wave.o ./BSP/AD9850/ad9850.o ./BSP/LCD/lcd.o ./BSP/LCD/lcd_init.o ./Board/board.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x6885f48e</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\2.8_lcd_adc_NOLVGL_DDS.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x6a69</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\.\</path>
         <kind>object</kind>
         <file>wave.o</file>
         <name>wave.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\.\BSP\AD9850\</path>
         <kind>object</kind>
         <file>ad9850.o</file>
         <name>ad9850.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\.\BSP\LCD\</path>
         <kind>object</kind>
         <file>lcd.o</file>
         <name>lcd.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\.\BSP\LCD\</path>
         <kind>object</kind>
         <file>lcd_init.o</file>
         <name>lcd_init.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\.\Board\</path>
         <kind>object</kind>
         <file>board.o</file>
         <name>board.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>C:\Users\<USER>\workspace_ccstheia\2.8_lcd_adc_NOLVGL_DDS\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-17">
         <path>G:\ti\SDK\mspm0-sdk-main\source\third_party\CMSIS\DSP\lib\ticlang\m0p\</path>
         <kind>archive</kind>
         <file>arm_cortexM0l_math.a</file>
         <name>arm_offset_f32.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>G:\ti\SDK\mspm0-sdk-main\source\third_party\CMSIS\DSP\lib\ticlang\m0p\</path>
         <kind>archive</kind>
         <file>arm_cortexM0l_math.a</file>
         <name>arm_mean_f32.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>G:\ti\SDK\mspm0-sdk-main\source\third_party\CMSIS\DSP\lib\ticlang\m0p\</path>
         <kind>archive</kind>
         <file>arm_cortexM0l_math.a</file>
         <name>arm_rms_f32.o</name>
      </input_file>
      <input_file id="fl-1a">
         <path>G:\ti\SDK\mspm0-sdk-main\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-1b">
         <path>G:\ti\SDK\mspm0-sdk-main\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-1c">
         <path>G:\ti\SDK\mspm0-sdk-main\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_dma.o</name>
      </input_file>
      <input_file id="fl-1d">
         <path>G:\ti\SDK\mspm0-sdk-main\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-1e">
         <path>G:\ti\SDK\mspm0-sdk-main\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-1f">
         <path>G:\ti\SDK\mspm0-sdk-main\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-20">
         <path>G:\ti\SDK\mspm0-sdk-main\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-37">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_sqrtf.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-53">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-54">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-55">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-56">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-f6">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-f7">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-f8">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-f9">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-fa">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-fb">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-fc">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixunsdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsidf.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-116">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>truncdfsf2.S.obj</name>
      </input_file>
      <input_file id="fl-117">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-118">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-119">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-11a">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-11b">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-11c">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-11d">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-11e">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-11f">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-120">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-121">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-122">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-123">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-124">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-125">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-126">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text.main</name>
         <load_address>0x3be0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3be0</run_address>
         <size>0x634</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.LCD_Init</name>
         <load_address>0x4214</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4214</run_address>
         <size>0x260</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.LCD_ShowChar</name>
         <load_address>0x4474</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4474</run_address>
         <size>0x214</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.LCD_ShowChinese12x12</name>
         <load_address>0x4688</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4688</run_address>
         <size>0x1d8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.LCD_ShowChinese16x16</name>
         <load_address>0x4860</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4860</run_address>
         <size>0x1d8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.LCD_ShowChinese24x24</name>
         <load_address>0x4a38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4a38</run_address>
         <size>0x1d8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.text.LCD_ShowChinese32x32</name>
         <load_address>0x4c10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4c10</run_address>
         <size>0x1d8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.text.adddf3_subdf3</name>
         <load_address>0x4de8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4de8</run_address>
         <size>0x192</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.LCD_DrawLine</name>
         <load_address>0x4f7a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4f7a</run_address>
         <size>0x128</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1e0">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x50a2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x50a2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text.LCD_ShowFloatNum1</name>
         <load_address>0x50a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x50a4</run_address>
         <size>0x11c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text.LCD_ShowChinese</name>
         <load_address>0x51c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x51c0</run_address>
         <size>0x116</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.text.__divdf3</name>
         <load_address>0x52d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x52d8</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text.dds_set</name>
         <load_address>0x53e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x53e4</run_address>
         <size>0x108</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-194">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x54ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x54ec</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.__muldf3</name>
         <load_address>0x55d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x55d4</run_address>
         <size>0xe4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-144">
         <name>.text</name>
         <load_address>0x56b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x56b8</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.arm_rms_f32</name>
         <load_address>0x5790</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5790</run_address>
         <size>0xd2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.arm_offset_f32</name>
         <load_address>0x5862</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5862</run_address>
         <size>0xb4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x5918</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5918</run_address>
         <size>0xb0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-101">
         <name>.text.SYSCFG_DL_ADC12_1_init</name>
         <load_address>0x59c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x59c8</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-152">
         <name>.text.sqrtf</name>
         <load_address>0x5a68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5a68</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-100">
         <name>.text.SYSCFG_DL_ADC12_0_init</name>
         <load_address>0x5b08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b08</run_address>
         <size>0x9c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x5ba4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5ba4</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.__mulsf3</name>
         <load_address>0x5c30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5c30</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.text.arm_mean_f32</name>
         <load_address>0x5cbc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5cbc</run_address>
         <size>0x88</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-97">
         <name>.text.LCD_Fill</name>
         <load_address>0x5d44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d44</run_address>
         <size>0x84</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x5dc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5dc8</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.__divsf3</name>
         <load_address>0x5e4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5e4c</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x5ed0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5ed0</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.text.LCD_ShowString</name>
         <load_address>0x5f4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5f4c</run_address>
         <size>0x7a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x5fc6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5fc6</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.__truncdfsf2</name>
         <load_address>0x5fd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5fd0</run_address>
         <size>0x74</size>
         <alignment>0x10</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-188">
         <name>.text.DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <load_address>0x6044</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6044</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-1c9">
         <name>.text.LCD_Writ_Bus</name>
         <load_address>0x60a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x60a8</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0x610c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x610c</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x6170</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6170</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.LCD_Address_Set</name>
         <load_address>0x61c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x61c8</run_address>
         <size>0x52</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.text.DL_ADC12_initSingleSample</name>
         <load_address>0x621c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x621c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-20e">
         <name>.text.DL_DMA_initChannel</name>
         <load_address>0x6268</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6268</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x62b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x62b4</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.text.DL_ADC12_configConversionMem</name>
         <load_address>0x6300</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6300</run_address>
         <size>0x4a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.text.DL_UART_init</name>
         <load_address>0x634c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x634c</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-1ac">
         <name>.text.DL_SPI_init</name>
         <load_address>0x6394</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6394</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-83">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x63d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x63d8</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.text.__fixunsdfsi</name>
         <load_address>0x641c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x641c</run_address>
         <size>0x42</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0x6460</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6460</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.SYSCFG_DL_Console_init</name>
         <load_address>0x64a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x64a0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.SYSCFG_DL_TFTspi_init</name>
         <load_address>0x64e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x64e0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x6520</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6520</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.__extendsfdf2</name>
         <load_address>0x6560</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6560</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.text.__floatsisf</name>
         <load_address>0x65a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x65a0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-1ec">
         <name>.text.__gtsf2</name>
         <load_address>0x65dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x65dc</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.dds_databitwrite</name>
         <load_address>0x6618</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6618</run_address>
         <size>0x3c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x6654</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6654</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-1e7">
         <name>.text.__eqsf2</name>
         <load_address>0x6690</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6690</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-159">
         <name>.text.__muldsi3</name>
         <load_address>0x66cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x66cc</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-197">
         <name>.text.DL_Timer_setPublisherChanID</name>
         <load_address>0x6708</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6708</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text.__fixsfsi</name>
         <load_address>0x6740</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6740</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text.dds_reset</name>
         <load_address>0x6778</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6778</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text.DL_DMA_setTransferSize</name>
         <load_address>0x67ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x67ac</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-20d">
         <name>.text.DL_DMA_setTransferSize</name>
         <load_address>0x67dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x67dc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ae">
         <name>.text.DL_SPI_setFIFOThreshold</name>
         <load_address>0x680c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x680c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.LCD_WR_REG</name>
         <load_address>0x683c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x683c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.text.mypow</name>
         <load_address>0x686c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x686c</run_address>
         <size>0x30</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.text.DL_ADC12_setDMASamplesCnt</name>
         <load_address>0x689c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x689c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.LCD_DrawPoint</name>
         <load_address>0x68c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x68c8</run_address>
         <size>0x2c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1c7">
         <name>.text.SYSCFG_DL_DMA_CH1_init</name>
         <load_address>0x68f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x68f4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x6920</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6920</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x694c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x694c</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-202">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x6976</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6976</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text.DL_DMA_setDestAddr</name>
         <load_address>0x69a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x69a0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-94">
         <name>.text.DL_DMA_setSrcAddr</name>
         <load_address>0x69c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x69c8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-196">
         <name>.text.DL_Timer_enableEvent</name>
         <load_address>0x69f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x69f0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c6">
         <name>.text.SYSCFG_DL_DMA_CH0_init</name>
         <load_address>0x6a18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a18</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text.__floatunsisf</name>
         <load_address>0x6a40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a40</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x6a68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a68</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text.DL_DMA_enableChannel</name>
         <load_address>0x6a90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a90</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.DL_ADC12_getMemResultAddress</name>
         <load_address>0x6ab8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ab8</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1ad">
         <name>.text.DL_SPI_setBitRateSerialClockDivider</name>
         <load_address>0x6adc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6adc</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-116">
         <name>.text.__floatunsidf</name>
         <load_address>0x6b00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b00</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.ADC0_IRQHandler</name>
         <load_address>0x6b24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b24</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.ADC1_IRQHandler</name>
         <load_address>0x6b44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b44</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-180">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x6b64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b64</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.text.DL_ADC12_setPowerDownMode</name>
         <load_address>0x6b84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b84</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x6ba2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ba2</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-135">
         <name>.text.LCD_WR_DATA</name>
         <load_address>0x6bc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6bc0</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.text.DL_ADC12_clearInterruptStatus</name>
         <load_address>0x6be0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6be0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.text.DL_ADC12_enableDMA</name>
         <load_address>0x6bfc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6bfc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1be">
         <name>.text.DL_ADC12_enableDMATrigger</name>
         <load_address>0x6c18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c18</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.text.DL_ADC12_enableInterrupt</name>
         <load_address>0x6c34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c34</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x6c50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c50</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-185">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x6c6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c6c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x6c88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c88</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-1bf">
         <name>.text.DL_ADC12_setSubscriberChanID</name>
         <load_address>0x6ca4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ca4</run_address>
         <size>0x1a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.text.DL_ADC12_enablePower</name>
         <load_address>0x6cc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6cc0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-178">
         <name>.text.DL_ADC12_reset</name>
         <load_address>0x6cd8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6cd8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>.text.DL_ADC12_setSampleTime0</name>
         <load_address>0x6cf0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6cf0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-183">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x6d08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6d08</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-179">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x6d20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6d20</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-181">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x6d38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6d38</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.text.DL_GPIO_initPeripheralAnalogFunction</name>
         <load_address>0x6d50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6d50</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-174">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x6d68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6d68</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x6d80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6d80</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x6d98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6d98</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-1af">
         <name>.text.DL_SPI_enable</name>
         <load_address>0x6db0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6db0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.text.DL_SPI_enablePower</name>
         <load_address>0x6dc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6dc8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-215">
         <name>.text.DL_SPI_isBusy</name>
         <load_address>0x6de0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6de0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-177">
         <name>.text.DL_SPI_reset</name>
         <load_address>0x6df8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6df8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-184">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x6e10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e10</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x6e28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e28</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-175">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x6e40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e40</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x6e58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e58</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x6e70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e70</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-176">
         <name>.text.DL_UART_reset</name>
         <load_address>0x6e88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e88</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.SYSCFG_DL_SYSCTL_CLK_init</name>
         <load_address>0x6ea0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ea0</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c3">
         <name>.text.DL_ADC12_enableConversions</name>
         <load_address>0x6eb8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6eb8</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-214">
         <name>.text.DL_SPI_transmitData8</name>
         <load_address>0x6ece</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ece</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.text.DL_UART_enable</name>
         <load_address>0x6ee4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ee4</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text.delay_ms</name>
         <load_address>0x6efa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6efa</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x6f10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f10</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x6f24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f24</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-182">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x6f38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f38</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-216">
         <name>.text.DL_SPI_receiveData8</name>
         <load_address>0x6f4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f4c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-187">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x6f60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f60</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-195">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x6f74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f74</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.LCD_WR_DATA8</name>
         <load_address>0x6f88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f88</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.dds_clkclr</name>
         <load_address>0x6f9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f9c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1d7">
         <name>.text.dds_clkset</name>
         <load_address>0x6fb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6fb0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1d5">
         <name>.text.dds_datclr</name>
         <load_address>0x6fc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6fc4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1d4">
         <name>.text.dds_datset</name>
         <load_address>0x6fd8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6fd8</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text.DL_ADC12_getPendingInterrupt</name>
         <load_address>0x6fec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6fec</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x6ffe</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ffe</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x7010</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7010</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x7022</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7022</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-124"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x7034</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7034</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-186">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x7048</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7048</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-52">
         <name>.text:decompress:ZI</name>
         <load_address>0x7058</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7058</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.text:TI_memset_small</name>
         <load_address>0x7068</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7068</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.text.DL_SYSCTL_getClockStatus</name>
         <load_address>0x7078</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7078</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.SYSCFG_DL_DMA_init</name>
         <load_address>0x7084</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7084</run_address>
         <size>0xc</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-65">
         <name>.text.__aeabi_memclr</name>
         <load_address>0x7090</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7090</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-1d6">
         <name>.text.dds_clkdelay</name>
         <load_address>0x709c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x709c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-1ee">
         <name>.text.__aeabi_errno_addr</name>
         <load_address>0x70a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70a8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x70b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70b0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text:abort</name>
         <load_address>0x70b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70b8</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x70be</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70be</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-163">
         <name>.text.HOSTexit</name>
         <load_address>0x70c2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70c2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f6"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x70c6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70c6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.text._system_pre_init</name>
         <load_address>0x70ca</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70ca</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-263">
         <name>.cinit..data.load</name>
         <load_address>0x70d0</load_address>
         <readonly>true</readonly>
         <run_address>0x70d0</run_address>
         <size>0xd</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-261">
         <name>__TI_handler_table</name>
         <load_address>0x70e0</load_address>
         <readonly>true</readonly>
         <run_address>0x70e0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-264">
         <name>.cinit..bss.load</name>
         <load_address>0x70ec</load_address>
         <readonly>true</readonly>
         <run_address>0x70ec</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-262">
         <name>__TI_cinit_table</name>
         <load_address>0x70f4</load_address>
         <readonly>true</readonly>
         <run_address>0x70f4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1d8">
         <name>.rodata.ascii_3216</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <run_address>0xc0</run_address>
         <size>0x17c0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1d9">
         <name>.rodata.ascii_2412</name>
         <load_address>0x1880</load_address>
         <readonly>true</readonly>
         <run_address>0x1880</run_address>
         <size>0x11d0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1da">
         <name>.rodata.ascii_1608</name>
         <load_address>0x2a50</load_address>
         <readonly>true</readonly>
         <run_address>0x2a50</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1db">
         <name>.rodata.ascii_1206</name>
         <load_address>0x3040</load_address>
         <readonly>true</readonly>
         <run_address>0x3040</run_address>
         <size>0x474</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1df">
         <name>.rodata.tfont32</name>
         <load_address>0x34b4</load_address>
         <readonly>true</readonly>
         <run_address>0x34b4</run_address>
         <size>0x28a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1dd">
         <name>.rodata.tfont16</name>
         <load_address>0x373e</load_address>
         <readonly>true</readonly>
         <run_address>0x373e</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1de">
         <name>.rodata.tfont24</name>
         <load_address>0x38d6</load_address>
         <readonly>true</readonly>
         <run_address>0x38d6</run_address>
         <size>0x172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-1dc">
         <name>.rodata.tfont12</name>
         <load_address>0x3a48</load_address>
         <readonly>true</readonly>
         <run_address>0x3a48</run_address>
         <size>0xbd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.rodata.str1.16704889451495720520.1</name>
         <load_address>0x3b05</load_address>
         <readonly>true</readonly>
         <run_address>0x3b05</run_address>
         <size>0x19</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.rodata.gConsoleClockConfig</name>
         <load_address>0x3b1e</load_address>
         <readonly>true</readonly>
         <run_address>0x3b1e</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-212">
         <name>.rodata.gDMA_CH0Config</name>
         <load_address>0x3b20</load_address>
         <readonly>true</readonly>
         <run_address>0x3b20</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-213">
         <name>.rodata.gDMA_CH1Config</name>
         <load_address>0x3b38</load_address>
         <readonly>true</readonly>
         <run_address>0x3b38</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-199">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0x3b50</load_address>
         <readonly>true</readonly>
         <run_address>0x3b50</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.rodata.str1.11898133897667081452.1</name>
         <load_address>0x3b64</load_address>
         <readonly>true</readonly>
         <run_address>0x3b64</run_address>
         <size>0xf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.rodata.str1.17669528882079347314.1</name>
         <load_address>0x3b73</load_address>
         <readonly>true</readonly>
         <run_address>0x3b73</run_address>
         <size>0xd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.rodata.str1.7401042497206923953.1</name>
         <load_address>0x3b80</load_address>
         <readonly>true</readonly>
         <run_address>0x3b80</run_address>
         <size>0xd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1a5">
         <name>.rodata.gConsoleConfig</name>
         <load_address>0x3b8e</load_address>
         <readonly>true</readonly>
         <run_address>0x3b8e</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.rodata.gTFTspi_config</name>
         <load_address>0x3b98</load_address>
         <readonly>true</readonly>
         <run_address>0x3b98</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.rodata.str1.14685083708502177989.1</name>
         <load_address>0x3ba2</load_address>
         <readonly>true</readonly>
         <run_address>0x3ba2</run_address>
         <size>0xa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1c4">
         <name>.rodata.gADC12_0ClockConfig</name>
         <load_address>0x3bac</load_address>
         <readonly>true</readonly>
         <run_address>0x3bac</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c5">
         <name>.rodata.gADC12_1ClockConfig</name>
         <load_address>0x3bb4</load_address>
         <readonly>true</readonly>
         <run_address>0x3bb4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.rodata.str1.254342170260855183.1</name>
         <load_address>0x3bbc</load_address>
         <readonly>true</readonly>
         <run_address>0x3bbc</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.rodata.str1.9517790425240694019.1</name>
         <load_address>0x3bc4</load_address>
         <readonly>true</readonly>
         <run_address>0x3bc4</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.rodata.str1.2196762768037919588.1</name>
         <load_address>0x3bcc</load_address>
         <readonly>true</readonly>
         <run_address>0x3bcc</run_address>
         <size>0x4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-198">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0x3bd0</load_address>
         <readonly>true</readonly>
         <run_address>0x3bd0</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b0">
         <name>.rodata.gTFTspi_clockConfig</name>
         <load_address>0x3bd3</load_address>
         <readonly>true</readonly>
         <run_address>0x3bd3</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.rodata.str1.150872071346279890.1</name>
         <load_address>0x3bd5</load_address>
         <readonly>true</readonly>
         <run_address>0x3bd5</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.rodata.str1.15706828512682300538.1</name>
         <load_address>0x3bd7</load_address>
         <readonly>true</readonly>
         <run_address>0x3bd7</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-22b">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-c1">
         <name>.data.gADCSamples</name>
         <load_address>0x202021e4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202021e4</run_address>
         <size>0x800</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.data.adc0_done</name>
         <load_address>0x202031e8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202031e8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.data.gADCSamples_ch1</name>
         <load_address>0x202029e4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202029e4</run_address>
         <size>0x800</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-60">
         <name>.data.adc1_done</name>
         <load_address>0x202031ec</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202031ec</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.data.g_current_freq_hz</name>
         <load_address>0x202031f0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202031f0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.data.g_has_prev_points</name>
         <load_address>0x202031f4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202031f4</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-21b">
         <name>.data.__aeabi_errno</name>
         <load_address>0x202031e4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202031e4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.bss.main.float_samples_current</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x1000</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.bss.main.float_samples_voltage</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20201000</run_address>
         <size>0x1000</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.bss.g_prev_points</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202020bc</run_address>
         <size>0x80</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.bss.g_prev_points_ch1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020213c</run_address>
         <size>0x80</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-104">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20202000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-105">
         <name>.common:gTFTspiBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202021bc</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-266">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1ac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_abbrev</name>
         <load_address>0x1ac</load_address>
         <run_address>0x1ac</run_address>
         <size>0x230</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x3dc</load_address>
         <run_address>0x3dc</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_abbrev</name>
         <load_address>0x449</load_address>
         <run_address>0x449</run_address>
         <size>0x133</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_abbrev</name>
         <load_address>0x57c</load_address>
         <run_address>0x57c</run_address>
         <size>0x132</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_abbrev</name>
         <load_address>0x6ae</load_address>
         <run_address>0x6ae</run_address>
         <size>0x130</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_abbrev</name>
         <load_address>0x7de</load_address>
         <run_address>0x7de</run_address>
         <size>0x54</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.debug_abbrev</name>
         <load_address>0x832</load_address>
         <run_address>0x832</run_address>
         <size>0x7d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-148">
         <name>.debug_abbrev</name>
         <load_address>0x8af</load_address>
         <run_address>0x8af</run_address>
         <size>0x7d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-156">
         <name>.debug_abbrev</name>
         <load_address>0x92c</load_address>
         <run_address>0x92c</run_address>
         <size>0xe6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-20b">
         <name>.debug_abbrev</name>
         <load_address>0xa12</load_address>
         <run_address>0xa12</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1ca">
         <name>.debug_abbrev</name>
         <load_address>0xb83</load_address>
         <run_address>0xb83</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-21f">
         <name>.debug_abbrev</name>
         <load_address>0xbe5</load_address>
         <run_address>0xbe5</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-209">
         <name>.debug_abbrev</name>
         <load_address>0xd65</load_address>
         <run_address>0xd65</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-205">
         <name>.debug_abbrev</name>
         <load_address>0xfdc</load_address>
         <run_address>0xfdc</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-207">
         <name>.debug_abbrev</name>
         <load_address>0x1262</load_address>
         <run_address>0x1262</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-203">
         <name>.debug_abbrev</name>
         <load_address>0x14fd</load_address>
         <run_address>0x14fd</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-1f3">
         <name>.debug_abbrev</name>
         <load_address>0x1715</load_address>
         <run_address>0x1715</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-21c">
         <name>.debug_abbrev</name>
         <load_address>0x17b9</load_address>
         <run_address>0x17b9</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0x1901</load_address>
         <run_address>0x1901</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.debug_abbrev</name>
         <load_address>0x19b0</load_address>
         <run_address>0x19b0</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_abbrev</name>
         <load_address>0x1b20</load_address>
         <run_address>0x1b20</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0x1b59</load_address>
         <run_address>0x1b59</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x1c1b</load_address>
         <run_address>0x1c1b</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0x1c8b</load_address>
         <run_address>0x1c8b</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-167">
         <name>.debug_abbrev</name>
         <load_address>0x1d18</load_address>
         <run_address>0x1d18</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-1f7">
         <name>.debug_abbrev</name>
         <load_address>0x1db0</load_address>
         <run_address>0x1db0</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f6"/>
      </object_component>
      <object_component id="oc-1e6">
         <name>.debug_abbrev</name>
         <load_address>0x1ddc</load_address>
         <run_address>0x1ddc</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-1d2">
         <name>.debug_abbrev</name>
         <load_address>0x1e03</load_address>
         <run_address>0x1e03</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-1d1">
         <name>.debug_abbrev</name>
         <load_address>0x1e2a</load_address>
         <run_address>0x1e2a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-1f6">
         <name>.debug_abbrev</name>
         <load_address>0x1e51</load_address>
         <run_address>0x1e51</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_abbrev</name>
         <load_address>0x1e78</load_address>
         <run_address>0x1e78</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.debug_abbrev</name>
         <load_address>0x1e9f</load_address>
         <run_address>0x1e9f</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.debug_abbrev</name>
         <load_address>0x1ec6</load_address>
         <run_address>0x1ec6</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-1d0">
         <name>.debug_abbrev</name>
         <load_address>0x1eed</load_address>
         <run_address>0x1eed</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_abbrev</name>
         <load_address>0x1f14</load_address>
         <run_address>0x1f14</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1d3">
         <name>.debug_abbrev</name>
         <load_address>0x1f3b</load_address>
         <run_address>0x1f3b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_abbrev</name>
         <load_address>0x1f62</load_address>
         <run_address>0x1f62</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.debug_abbrev</name>
         <load_address>0x1f89</load_address>
         <run_address>0x1f89</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.debug_abbrev</name>
         <load_address>0x1fb0</load_address>
         <run_address>0x1fb0</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.debug_abbrev</name>
         <load_address>0x1fd7</load_address>
         <run_address>0x1fd7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-1ed">
         <name>.debug_abbrev</name>
         <load_address>0x1ffe</load_address>
         <run_address>0x1ffe</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0x2025</load_address>
         <run_address>0x2025</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_abbrev</name>
         <load_address>0x204c</load_address>
         <run_address>0x204c</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-1e5">
         <name>.debug_abbrev</name>
         <load_address>0x2071</load_address>
         <run_address>0x2071</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-21a">
         <name>.debug_abbrev</name>
         <load_address>0x2098</load_address>
         <run_address>0x2098</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-217">
         <name>.debug_abbrev</name>
         <load_address>0x20bd</load_address>
         <run_address>0x20bd</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_abbrev</name>
         <load_address>0x2116</load_address>
         <run_address>0x2116</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-124"/>
      </object_component>
      <object_component id="oc-173">
         <name>.debug_abbrev</name>
         <load_address>0x213b</load_address>
         <run_address>0x213b</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-268">
         <name>.debug_abbrev</name>
         <load_address>0x2160</load_address>
         <run_address>0x2160</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x192b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_info</name>
         <load_address>0x192b</load_address>
         <run_address>0x192b</run_address>
         <size>0x4189</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x5ab4</load_address>
         <run_address>0x5ab4</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_info</name>
         <load_address>0x5b34</load_address>
         <run_address>0x5b34</run_address>
         <size>0x966</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_info</name>
         <load_address>0x649a</load_address>
         <run_address>0x649a</run_address>
         <size>0xedf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_info</name>
         <load_address>0x7379</load_address>
         <run_address>0x7379</run_address>
         <size>0x10f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_info</name>
         <load_address>0x8469</load_address>
         <run_address>0x8469</run_address>
         <size>0xe2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_info</name>
         <load_address>0x854b</load_address>
         <run_address>0x854b</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_info</name>
         <load_address>0x8611</load_address>
         <run_address>0x8611</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_info</name>
         <load_address>0x86d7</load_address>
         <run_address>0x86d7</run_address>
         <size>0x161</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.debug_info</name>
         <load_address>0x8838</load_address>
         <run_address>0x8838</run_address>
         <size>0x745</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_info</name>
         <load_address>0x8f7d</load_address>
         <run_address>0x8f7d</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-20f">
         <name>.debug_info</name>
         <load_address>0x8ff2</load_address>
         <run_address>0x8ff2</run_address>
         <size>0x6ea</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1a8">
         <name>.debug_info</name>
         <load_address>0x96dc</load_address>
         <run_address>0x96dc</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.debug_info</name>
         <load_address>0xa81e</load_address>
         <run_address>0xa81e</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.debug_info</name>
         <load_address>0xd990</load_address>
         <run_address>0xd990</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.debug_info</name>
         <load_address>0xec36</load_address>
         <run_address>0xec36</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-153">
         <name>.debug_info</name>
         <load_address>0xfcc6</load_address>
         <run_address>0xfcc6</run_address>
         <size>0x143</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-1f2">
         <name>.debug_info</name>
         <load_address>0xfe09</load_address>
         <run_address>0xfe09</run_address>
         <size>0x33d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x10146</load_address>
         <run_address>0x10146</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_info</name>
         <load_address>0x10569</load_address>
         <run_address>0x10569</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_info</name>
         <load_address>0x10cad</load_address>
         <run_address>0x10cad</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0x10cf3</load_address>
         <run_address>0x10cf3</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0x10e85</load_address>
         <run_address>0x10e85</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0x10f4b</load_address>
         <run_address>0x10f4b</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_info</name>
         <load_address>0x110c7</load_address>
         <run_address>0x110c7</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-165">
         <name>.debug_info</name>
         <load_address>0x111bf</load_address>
         <run_address>0x111bf</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f6"/>
      </object_component>
      <object_component id="oc-147">
         <name>.debug_info</name>
         <load_address>0x111fa</load_address>
         <run_address>0x111fa</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_info</name>
         <load_address>0x113a1</load_address>
         <run_address>0x113a1</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-127">
         <name>.debug_info</name>
         <load_address>0x11548</load_address>
         <run_address>0x11548</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.debug_info</name>
         <load_address>0x116d5</load_address>
         <run_address>0x116d5</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_info</name>
         <load_address>0x11864</load_address>
         <run_address>0x11864</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_info</name>
         <load_address>0x119f1</load_address>
         <run_address>0x119f1</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_info</name>
         <load_address>0x11b7e</load_address>
         <run_address>0x11b7e</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-124">
         <name>.debug_info</name>
         <load_address>0x11d0b</load_address>
         <run_address>0x11d0b</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_info</name>
         <load_address>0x11ea2</load_address>
         <run_address>0x11ea2</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_info</name>
         <load_address>0x12031</load_address>
         <run_address>0x12031</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_info</name>
         <load_address>0x121c6</load_address>
         <run_address>0x121c6</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_info</name>
         <load_address>0x12359</load_address>
         <run_address>0x12359</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_info</name>
         <load_address>0x124f0</load_address>
         <run_address>0x124f0</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_info</name>
         <load_address>0x12687</load_address>
         <run_address>0x12687</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.debug_info</name>
         <load_address>0x1281c</load_address>
         <run_address>0x1281c</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0x12a33</load_address>
         <run_address>0x12a33</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_info</name>
         <load_address>0x12bcc</load_address>
         <run_address>0x12bcc</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_info</name>
         <load_address>0x12d81</load_address>
         <run_address>0x12d81</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-1ea">
         <name>.debug_info</name>
         <load_address>0x12f3d</load_address>
         <run_address>0x12f3d</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1e1">
         <name>.debug_info</name>
         <load_address>0x130fe</load_address>
         <run_address>0x130fe</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_info</name>
         <load_address>0x13183</load_address>
         <run_address>0x13183</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-124"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_info</name>
         <load_address>0x1347d</load_address>
         <run_address>0x1347d</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-267">
         <name>.debug_info</name>
         <load_address>0x136c1</load_address>
         <run_address>0x136c1</run_address>
         <size>0xb6</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x60</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_ranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x1e0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x240</load_address>
         <run_address>0x240</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_ranges</name>
         <load_address>0x258</load_address>
         <run_address>0x258</run_address>
         <size>0x58</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_ranges</name>
         <load_address>0x2b0</load_address>
         <run_address>0x2b0</run_address>
         <size>0x88</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_ranges</name>
         <load_address>0x338</load_address>
         <run_address>0x338</run_address>
         <size>0x68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_ranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.debug_ranges</name>
         <load_address>0x3c8</load_address>
         <run_address>0x3c8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1a9">
         <name>.debug_ranges</name>
         <load_address>0x3e0</load_address>
         <run_address>0x3e0</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-191">
         <name>.debug_ranges</name>
         <load_address>0x770</load_address>
         <run_address>0x770</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.debug_ranges</name>
         <load_address>0x948</load_address>
         <run_address>0x948</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-189">
         <name>.debug_ranges</name>
         <load_address>0xaf0</load_address>
         <run_address>0xaf0</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-1f1">
         <name>.debug_ranges</name>
         <load_address>0xc98</load_address>
         <run_address>0xc98</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0xcc8</load_address>
         <run_address>0xcc8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_ranges</name>
         <load_address>0xd10</load_address>
         <run_address>0xd10</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_ranges</name>
         <load_address>0xd58</load_address>
         <run_address>0xd58</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0xd70</load_address>
         <run_address>0xd70</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_ranges</name>
         <load_address>0xdc0</load_address>
         <run_address>0xdc0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_ranges</name>
         <load_address>0xdd8</load_address>
         <run_address>0xdd8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-1e8">
         <name>.debug_ranges</name>
         <load_address>0xe00</load_address>
         <run_address>0xe00</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1e4">
         <name>.debug_ranges</name>
         <load_address>0xe38</load_address>
         <run_address>0xe38</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_ranges</name>
         <load_address>0xe50</load_address>
         <run_address>0xe50</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-124"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_ranges</name>
         <load_address>0xe78</load_address>
         <run_address>0xe78</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xebc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_str</name>
         <load_address>0xebc</load_address>
         <run_address>0xebc</run_address>
         <size>0x34ed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x43a9</load_address>
         <run_address>0x43a9</run_address>
         <size>0x16b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_str</name>
         <load_address>0x4514</load_address>
         <run_address>0x4514</run_address>
         <size>0x533</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_str</name>
         <load_address>0x4a47</load_address>
         <run_address>0x4a47</run_address>
         <size>0x3cd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_str</name>
         <load_address>0x4e14</load_address>
         <run_address>0x4e14</run_address>
         <size>0x61e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_str</name>
         <load_address>0x5432</load_address>
         <run_address>0x5432</run_address>
         <size>0x116</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.debug_str</name>
         <load_address>0x5548</load_address>
         <run_address>0x5548</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-149">
         <name>.debug_str</name>
         <load_address>0x56ef</load_address>
         <run_address>0x56ef</run_address>
         <size>0x1a4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_str</name>
         <load_address>0x5893</load_address>
         <run_address>0x5893</run_address>
         <size>0x262</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-20c">
         <name>.debug_str</name>
         <load_address>0x5af5</load_address>
         <run_address>0x5af5</run_address>
         <size>0x63b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.debug_str</name>
         <load_address>0x6130</load_address>
         <run_address>0x6130</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-220">
         <name>.debug_str</name>
         <load_address>0x62a7</load_address>
         <run_address>0x62a7</run_address>
         <size>0x654</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-20a">
         <name>.debug_str</name>
         <load_address>0x68fb</load_address>
         <run_address>0x68fb</run_address>
         <size>0xc45</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-206">
         <name>.debug_str</name>
         <load_address>0x7540</load_address>
         <run_address>0x7540</run_address>
         <size>0x1dd6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-208">
         <name>.debug_str</name>
         <load_address>0x9316</load_address>
         <run_address>0x9316</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-204">
         <name>.debug_str</name>
         <load_address>0xa003</load_address>
         <run_address>0xa003</run_address>
         <size>0x107f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-1f4">
         <name>.debug_str</name>
         <load_address>0xb082</load_address>
         <run_address>0xb082</run_address>
         <size>0x156</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-21d">
         <name>.debug_str</name>
         <load_address>0xb1d8</load_address>
         <run_address>0xb1d8</run_address>
         <size>0x332</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0xb50a</load_address>
         <run_address>0xb50a</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_str</name>
         <load_address>0xb72f</load_address>
         <run_address>0xb72f</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_str</name>
         <load_address>0xba5e</load_address>
         <run_address>0xba5e</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_str</name>
         <load_address>0xbb53</load_address>
         <run_address>0xbb53</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0xbcee</load_address>
         <run_address>0xbcee</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0xbe56</load_address>
         <run_address>0xbe56</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-168">
         <name>.debug_str</name>
         <load_address>0xc02b</load_address>
         <run_address>0xc02b</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-1f8">
         <name>.debug_str</name>
         <load_address>0xc173</load_address>
         <run_address>0xc173</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f6"/>
      </object_component>
      <object_component id="oc-218">
         <name>.debug_str</name>
         <load_address>0xc25c</load_address>
         <run_address>0xc25c</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_frame</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x560</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x660</load_address>
         <run_address>0x660</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_frame</name>
         <load_address>0x690</load_address>
         <run_address>0x690</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_frame</name>
         <load_address>0x794</load_address>
         <run_address>0x794</run_address>
         <size>0x1f8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_frame</name>
         <load_address>0x98c</load_address>
         <run_address>0x98c</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_frame</name>
         <load_address>0xab8</load_address>
         <run_address>0xab8</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_frame</name>
         <load_address>0xb38</load_address>
         <run_address>0xb38</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_frame</name>
         <load_address>0xb68</load_address>
         <run_address>0xb68</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_frame</name>
         <load_address>0xb98</load_address>
         <run_address>0xb98</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.debug_frame</name>
         <load_address>0xbc8</load_address>
         <run_address>0xbc8</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_frame</name>
         <load_address>0xc14</load_address>
         <run_address>0xc14</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-210">
         <name>.debug_frame</name>
         <load_address>0xc34</load_address>
         <run_address>0xc34</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1ab">
         <name>.debug_frame</name>
         <load_address>0xc64</load_address>
         <run_address>0xc64</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_frame</name>
         <load_address>0xe98</load_address>
         <run_address>0xe98</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.debug_frame</name>
         <load_address>0x12a0</load_address>
         <run_address>0x12a0</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.debug_frame</name>
         <load_address>0x1458</load_address>
         <run_address>0x1458</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-155">
         <name>.debug_frame</name>
         <load_address>0x1584</load_address>
         <run_address>0x1584</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-1ef">
         <name>.debug_frame</name>
         <load_address>0x15b4</load_address>
         <run_address>0x15b4</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x1624</load_address>
         <run_address>0x1624</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_frame</name>
         <load_address>0x16b4</load_address>
         <run_address>0x16b4</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_frame</name>
         <load_address>0x17b4</load_address>
         <run_address>0x17b4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_frame</name>
         <load_address>0x17d4</load_address>
         <run_address>0x17d4</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x180c</load_address>
         <run_address>0x180c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0x1834</load_address>
         <run_address>0x1834</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_frame</name>
         <load_address>0x1864</load_address>
         <run_address>0x1864</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-166">
         <name>.debug_frame</name>
         <load_address>0x1894</load_address>
         <run_address>0x1894</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f6"/>
      </object_component>
      <object_component id="oc-1e2">
         <name>.debug_frame</name>
         <load_address>0x18b4</load_address>
         <run_address>0x18b4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7d0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_line</name>
         <load_address>0x7d0</load_address>
         <run_address>0x7d0</run_address>
         <size>0xe28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x15f8</load_address>
         <run_address>0x15f8</run_address>
         <size>0xb6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_line</name>
         <load_address>0x16ae</load_address>
         <run_address>0x16ae</run_address>
         <size>0x3db</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0x1a89</load_address>
         <run_address>0x1a89</run_address>
         <size>0x1233</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x2cbc</load_address>
         <run_address>0x2cbc</run_address>
         <size>0x5c5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_line</name>
         <load_address>0x3281</load_address>
         <run_address>0x3281</run_address>
         <size>0x91</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_line</name>
         <load_address>0x3312</load_address>
         <run_address>0x3312</run_address>
         <size>0x257</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_line</name>
         <load_address>0x3569</load_address>
         <run_address>0x3569</run_address>
         <size>0x233</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_line</name>
         <load_address>0x379c</load_address>
         <run_address>0x379c</run_address>
         <size>0x24b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.debug_line</name>
         <load_address>0x39e7</load_address>
         <run_address>0x39e7</run_address>
         <size>0x280</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_line</name>
         <load_address>0x3c67</load_address>
         <run_address>0x3c67</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-211">
         <name>.debug_line</name>
         <load_address>0x3de0</load_address>
         <run_address>0x3de0</run_address>
         <size>0x249</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1aa">
         <name>.debug_line</name>
         <load_address>0x4029</load_address>
         <run_address>0x4029</run_address>
         <size>0xc1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-190">
         <name>.debug_line</name>
         <load_address>0x4c44</load_address>
         <run_address>0x4c44</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.debug_line</name>
         <load_address>0x63b3</load_address>
         <run_address>0x63b3</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.debug_line</name>
         <load_address>0x6dcb</load_address>
         <run_address>0x6dcb</run_address>
         <size>0x983</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-154">
         <name>.debug_line</name>
         <load_address>0x774e</load_address>
         <run_address>0x774e</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-1f0">
         <name>.debug_line</name>
         <load_address>0x7935</load_address>
         <run_address>0x7935</run_address>
         <size>0x144</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x7a79</load_address>
         <run_address>0x7a79</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_line</name>
         <load_address>0x7c55</load_address>
         <run_address>0x7c55</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_line</name>
         <load_address>0x816f</load_address>
         <run_address>0x816f</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_line</name>
         <load_address>0x81ad</load_address>
         <run_address>0x81ad</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x82ab</load_address>
         <run_address>0x82ab</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_line</name>
         <load_address>0x836b</load_address>
         <run_address>0x836b</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_line</name>
         <load_address>0x8533</load_address>
         <run_address>0x8533</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_line</name>
         <load_address>0x859a</load_address>
         <run_address>0x859a</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f6"/>
      </object_component>
      <object_component id="oc-146">
         <name>.debug_line</name>
         <load_address>0x85db</load_address>
         <run_address>0x85db</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_line</name>
         <load_address>0x86e2</load_address>
         <run_address>0x86e2</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_line</name>
         <load_address>0x8847</load_address>
         <run_address>0x8847</run_address>
         <size>0x10c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.debug_line</name>
         <load_address>0x8953</load_address>
         <run_address>0x8953</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_line</name>
         <load_address>0x8a0c</load_address>
         <run_address>0x8a0c</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_line</name>
         <load_address>0x8aec</load_address>
         <run_address>0x8aec</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_line</name>
         <load_address>0x8bc8</load_address>
         <run_address>0x8bc8</run_address>
         <size>0x122</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_line</name>
         <load_address>0x8cea</load_address>
         <run_address>0x8cea</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_line</name>
         <load_address>0x8daa</load_address>
         <run_address>0x8daa</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_line</name>
         <load_address>0x8e62</load_address>
         <run_address>0x8e62</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_line</name>
         <load_address>0x8f22</load_address>
         <run_address>0x8f22</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_line</name>
         <load_address>0x8fde</load_address>
         <run_address>0x8fde</run_address>
         <size>0xb2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_line</name>
         <load_address>0x9090</load_address>
         <run_address>0x9090</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_line</name>
         <load_address>0x9144</load_address>
         <run_address>0x9144</run_address>
         <size>0xd1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-151">
         <name>.debug_line</name>
         <load_address>0x9215</load_address>
         <run_address>0x9215</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_line</name>
         <load_address>0x92dc</load_address>
         <run_address>0x92dc</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_line</name>
         <load_address>0x9380</load_address>
         <run_address>0x9380</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_line</name>
         <load_address>0x943a</load_address>
         <run_address>0x943a</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-1eb">
         <name>.debug_line</name>
         <load_address>0x94fc</load_address>
         <run_address>0x94fc</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1e3">
         <name>.debug_line</name>
         <load_address>0x9600</load_address>
         <run_address>0x9600</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_line</name>
         <load_address>0x96b5</load_address>
         <run_address>0x96b5</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-124"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_line</name>
         <load_address>0x9755</load_address>
         <run_address>0x9755</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x11f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.debug_loc</name>
         <load_address>0x11f</load_address>
         <run_address>0x11f</run_address>
         <size>0x127</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_loc</name>
         <load_address>0x246</load_address>
         <run_address>0x246</run_address>
         <size>0xfc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.debug_loc</name>
         <load_address>0x342</load_address>
         <run_address>0x342</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.debug_loc</name>
         <load_address>0x409</load_address>
         <run_address>0x409</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-221">
         <name>.debug_loc</name>
         <load_address>0x41c</load_address>
         <run_address>0x41c</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.debug_loc</name>
         <load_address>0x4ec</load_address>
         <run_address>0x4ec</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_loc</name>
         <load_address>0xd02</load_address>
         <run_address>0xd02</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.debug_loc</name>
         <load_address>0x2729</load_address>
         <run_address>0x2729</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.debug_loc</name>
         <load_address>0x2ee5</load_address>
         <run_address>0x2ee5</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-1f5">
         <name>.debug_loc</name>
         <load_address>0x32f9</load_address>
         <run_address>0x32f9</run_address>
         <size>0x129</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-21e">
         <name>.debug_loc</name>
         <load_address>0x3422</load_address>
         <run_address>0x3422</run_address>
         <size>0x101</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x3523</load_address>
         <run_address>0x3523</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_loc</name>
         <load_address>0x35fb</load_address>
         <run_address>0x35fb</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x3a1f</load_address>
         <run_address>0x3a1f</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x3b8b</load_address>
         <run_address>0x3b8b</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x3bfa</load_address>
         <run_address>0x3bfa</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_loc</name>
         <load_address>0x3d61</load_address>
         <run_address>0x3d61</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-219">
         <name>.debug_loc</name>
         <load_address>0x3d87</load_address>
         <run_address>0x3d87</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-122"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-106"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-128">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10b"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_aranges</name>
         <load_address>0x160</load_address>
         <run_address>0x160</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_aranges</name>
         <load_address>0x180</load_address>
         <run_address>0x180</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-114"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_aranges</name>
         <load_address>0x1a0</load_address>
         <run_address>0x1a0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-150">
         <name>.debug_aranges</name>
         <load_address>0x1c0</load_address>
         <run_address>0x1c0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x1e0</load_address>
         <run_address>0x1e0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_aranges</name>
         <load_address>0x200</load_address>
         <run_address>0x200</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-141">
         <name>.debug_aranges</name>
         <load_address>0x228</load_address>
         <run_address>0x228</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11c"/>
      </object_component>
      <object_component id="oc-1e9">
         <name>.debug_aranges</name>
         <load_address>0x248</load_address>
         <run_address>0x248</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_aranges</name>
         <load_address>0x278</load_address>
         <run_address>0x278</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-124"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_aranges</name>
         <load_address>0x2a0</load_address>
         <run_address>0x2a0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0x3be0</load_address>
         <run_address>0x3be0</run_address>
         <size>0x34f0</size>
         <contents>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-1e0"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-1c2"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-20e"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-1ac"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-1ec"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-1e7"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-20d"/>
            <object_component_ref idref="oc-1ae"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-1c7"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-202"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-1c6"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-1ad"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-1c0"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-1c1"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-1bf"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-1af"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-215"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-1c3"/>
            <object_component_ref idref="oc-214"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-216"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-1d7"/>
            <object_component_ref idref="oc-1d5"/>
            <object_component_ref idref="oc-1d4"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-1a6"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-1d6"/>
            <object_component_ref idref="oc-1ee"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x70d0</load_address>
         <run_address>0x70d0</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-263"/>
            <object_component_ref idref="oc-261"/>
            <object_component_ref idref="oc-264"/>
            <object_component_ref idref="oc-262"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x3b20</size>
         <contents>
            <object_component_ref idref="oc-1d8"/>
            <object_component_ref idref="oc-1d9"/>
            <object_component_ref idref="oc-1da"/>
            <object_component_ref idref="oc-1db"/>
            <object_component_ref idref="oc-1df"/>
            <object_component_ref idref="oc-1dd"/>
            <object_component_ref idref="oc-1de"/>
            <object_component_ref idref="oc-1dc"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-1a4"/>
            <object_component_ref idref="oc-212"/>
            <object_component_ref idref="oc-213"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-1c4"/>
            <object_component_ref idref="oc-1c5"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-1b0"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-d5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-22b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202021e4</run_address>
         <size>0x1011</size>
         <contents>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-21b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x21e4</size>
         <contents>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-105"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-266"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-222" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-223" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-224" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-225" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-226" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-227" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-229" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-245" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x216f</size>
         <contents>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-20b"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-21f"/>
            <object_component_ref idref="oc-209"/>
            <object_component_ref idref="oc-205"/>
            <object_component_ref idref="oc-207"/>
            <object_component_ref idref="oc-203"/>
            <object_component_ref idref="oc-1f3"/>
            <object_component_ref idref="oc-21c"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-1f7"/>
            <object_component_ref idref="oc-1e6"/>
            <object_component_ref idref="oc-1d2"/>
            <object_component_ref idref="oc-1d1"/>
            <object_component_ref idref="oc-1f6"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-1d0"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-1d3"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-1cd"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-1cf"/>
            <object_component_ref idref="oc-1ed"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-1e5"/>
            <object_component_ref idref="oc-21a"/>
            <object_component_ref idref="oc-217"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-268"/>
         </contents>
      </logical_group>
      <logical_group id="lg-247" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13777</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-20f"/>
            <object_component_ref idref="oc-1a8"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-1f2"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-1ea"/>
            <object_component_ref idref="oc-1e1"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-267"/>
         </contents>
      </logical_group>
      <logical_group id="lg-249" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xea0</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-1a9"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-1f1"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-1e8"/>
            <object_component_ref idref="oc-1e4"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-f6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-24b" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc3ef</size>
         <contents>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-20c"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-220"/>
            <object_component_ref idref="oc-20a"/>
            <object_component_ref idref="oc-206"/>
            <object_component_ref idref="oc-208"/>
            <object_component_ref idref="oc-204"/>
            <object_component_ref idref="oc-1f4"/>
            <object_component_ref idref="oc-21d"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-1f8"/>
            <object_component_ref idref="oc-218"/>
         </contents>
      </logical_group>
      <logical_group id="lg-24d" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18e4</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-210"/>
            <object_component_ref idref="oc-1ab"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-1ef"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-1e2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-24f" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x97d5</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-211"/>
            <object_component_ref idref="oc-1aa"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-1f0"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-1eb"/>
            <object_component_ref idref="oc-1e3"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-f8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-251" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3da7</size>
         <contents>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-221"/>
            <object_component_ref idref="oc-1a7"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-1f5"/>
            <object_component_ref idref="oc-21e"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-219"/>
         </contents>
      </logical_group>
      <logical_group id="lg-25b" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2c8</size>
         <contents>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-1e9"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-f5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-265" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-27e" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7108</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-27f" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x31f5</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-280" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x7108</used_space>
         <unused_space>0x18ef8</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x3b20</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x3be0</start_address>
               <size>0x34f0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x70d0</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x7108</start_address>
               <size>0x18ef8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x33f5</used_space>
         <unused_space>0x4c0b</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-227"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-229"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x21e4</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202021e4</start_address>
               <size>0x1011</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202031f5</start_address>
               <size>0x4c0b</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x70d0</load_address>
            <load_size>0xd</load_size>
            <run_address>0x202021e4</run_address>
            <run_size>0x1011</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x70ec</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x21e4</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x70f4</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x7104</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x7104</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x70e0</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x70ec</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-78">
         <name>main</name>
         <value>0x3be1</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-79">
         <name>gADCSamples</name>
         <value>0x202021e4</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-7a">
         <name>gADCSamples_ch1</name>
         <value>0x202029e4</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-7b">
         <name>adc0_done</name>
         <value>0x202031e8</value>
         <object_component_ref idref="oc-5d"/>
      </symbol>
      <symbol id="sm-7c">
         <name>adc1_done</name>
         <value>0x202031ec</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-7d">
         <name>ADC0_IRQHandler</name>
         <value>0x6b25</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-7e">
         <name>ADC1_IRQHandler</name>
         <value>0x6b45</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-165">
         <name>SYSCFG_DL_init</name>
         <value>0x63d9</value>
         <object_component_ref idref="oc-83"/>
      </symbol>
      <symbol id="sm-166">
         <name>SYSCFG_DL_initPower</name>
         <value>0x5ba5</value>
         <object_component_ref idref="oc-fa"/>
      </symbol>
      <symbol id="sm-167">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x5919</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-168">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x694d</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-169">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x6171</value>
         <object_component_ref idref="oc-fd"/>
      </symbol>
      <symbol id="sm-16a">
         <name>SYSCFG_DL_Console_init</name>
         <value>0x64a1</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-16b">
         <name>SYSCFG_DL_TFTspi_init</name>
         <value>0x64e1</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-16c">
         <name>SYSCFG_DL_ADC12_0_init</name>
         <value>0x5b09</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-16d">
         <name>SYSCFG_DL_ADC12_1_init</name>
         <value>0x59c9</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-16e">
         <name>SYSCFG_DL_DMA_init</name>
         <value>0x7085</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-16f">
         <name>SYSCFG_DL_SYSCTL_CLK_init</name>
         <value>0x6ea1</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-170">
         <name>gTIMER_0Backup</name>
         <value>0x20202000</value>
      </symbol>
      <symbol id="sm-171">
         <name>gTFTspiBackup</name>
         <value>0x202021bc</value>
      </symbol>
      <symbol id="sm-172">
         <name>SYSCFG_DL_DMA_CH0_init</name>
         <value>0x6a19</value>
         <object_component_ref idref="oc-1c6"/>
      </symbol>
      <symbol id="sm-173">
         <name>SYSCFG_DL_DMA_CH1_init</name>
         <value>0x68f5</value>
         <object_component_ref idref="oc-1c7"/>
      </symbol>
      <symbol id="sm-17e">
         <name>Default_Handler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-17f">
         <name>Reset_Handler</name>
         <value>0x70c7</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-180">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-181">
         <name>NMI_Handler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-182">
         <name>HardFault_Handler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-183">
         <name>SVC_Handler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-184">
         <name>PendSV_Handler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-185">
         <name>SysTick_Handler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-186">
         <name>GROUP0_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-187">
         <name>GROUP1_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-188">
         <name>TIMG8_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-189">
         <name>UART3_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18a">
         <name>CANFD0_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18b">
         <name>DAC0_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18c">
         <name>SPI0_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18d">
         <name>SPI1_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18e">
         <name>UART1_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18f">
         <name>UART2_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-190">
         <name>UART0_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-191">
         <name>TIMG0_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-192">
         <name>TIMG6_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-193">
         <name>TIMA0_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-194">
         <name>TIMA1_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-195">
         <name>TIMG7_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-196">
         <name>TIMG12_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-197">
         <name>I2C0_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-198">
         <name>I2C1_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-199">
         <name>AES_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19a">
         <name>RTC_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19b">
         <name>DMA_IRQHandler</name>
         <value>0x70bf</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1c6">
         <name>dds_set</name>
         <value>0x53e5</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-1c7">
         <name>dds_reset</name>
         <value>0x6779</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-1ec">
         <name>LCD_Fill</name>
         <value>0x5d45</value>
         <object_component_ref idref="oc-97"/>
      </symbol>
      <symbol id="sm-1ed">
         <name>LCD_DrawPoint</name>
         <value>0x68c9</value>
         <object_component_ref idref="oc-160"/>
      </symbol>
      <symbol id="sm-1ee">
         <name>LCD_DrawLine</name>
         <value>0x4f7b</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-1ef">
         <name>LCD_ShowChinese</name>
         <value>0x51c1</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-1f0">
         <name>LCD_ShowChinese12x12</name>
         <value>0x4689</value>
         <object_component_ref idref="oc-13b"/>
      </symbol>
      <symbol id="sm-1f1">
         <name>LCD_ShowChinese16x16</name>
         <value>0x4861</value>
         <object_component_ref idref="oc-13c"/>
      </symbol>
      <symbol id="sm-1f2">
         <name>LCD_ShowChinese24x24</name>
         <value>0x4a39</value>
         <object_component_ref idref="oc-13d"/>
      </symbol>
      <symbol id="sm-1f3">
         <name>LCD_ShowChinese32x32</name>
         <value>0x4c11</value>
         <object_component_ref idref="oc-13e"/>
      </symbol>
      <symbol id="sm-1f4">
         <name>tfont12</name>
         <value>0x3a48</value>
         <object_component_ref idref="oc-1dc"/>
      </symbol>
      <symbol id="sm-1f5">
         <name>tfont16</name>
         <value>0x373e</value>
         <object_component_ref idref="oc-1dd"/>
      </symbol>
      <symbol id="sm-1f6">
         <name>tfont24</name>
         <value>0x38d6</value>
         <object_component_ref idref="oc-1de"/>
      </symbol>
      <symbol id="sm-1f7">
         <name>tfont32</name>
         <value>0x34b4</value>
         <object_component_ref idref="oc-1df"/>
      </symbol>
      <symbol id="sm-1f8">
         <name>LCD_ShowChar</name>
         <value>0x4475</value>
         <object_component_ref idref="oc-13a"/>
      </symbol>
      <symbol id="sm-1f9">
         <name>ascii_3216</name>
         <value>0xc0</value>
         <object_component_ref idref="oc-1d8"/>
      </symbol>
      <symbol id="sm-1fa">
         <name>ascii_2412</name>
         <value>0x1880</value>
         <object_component_ref idref="oc-1d9"/>
      </symbol>
      <symbol id="sm-1fb">
         <name>ascii_1608</name>
         <value>0x2a50</value>
         <object_component_ref idref="oc-1da"/>
      </symbol>
      <symbol id="sm-1fc">
         <name>ascii_1206</name>
         <value>0x3040</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-1fd">
         <name>LCD_ShowString</name>
         <value>0x5f4d</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-1fe">
         <name>mypow</name>
         <value>0x686d</value>
         <object_component_ref idref="oc-15e"/>
      </symbol>
      <symbol id="sm-1ff">
         <name>LCD_ShowFloatNum1</name>
         <value>0x50a5</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-227">
         <name>LCD_Writ_Bus</name>
         <value>0x60a9</value>
         <object_component_ref idref="oc-1c9"/>
      </symbol>
      <symbol id="sm-228">
         <name>LCD_WR_DATA8</name>
         <value>0x6f89</value>
         <object_component_ref idref="oc-10b"/>
      </symbol>
      <symbol id="sm-229">
         <name>LCD_WR_DATA</name>
         <value>0x6bc1</value>
         <object_component_ref idref="oc-135"/>
      </symbol>
      <symbol id="sm-22a">
         <name>LCD_WR_REG</name>
         <value>0x683d</value>
         <object_component_ref idref="oc-10a"/>
      </symbol>
      <symbol id="sm-22b">
         <name>LCD_Address_Set</name>
         <value>0x61c9</value>
         <object_component_ref idref="oc-134"/>
      </symbol>
      <symbol id="sm-22c">
         <name>LCD_Init</name>
         <value>0x4215</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-235">
         <name>delay_ms</name>
         <value>0x6efb</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-236">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-237">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-238">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-239">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-23a">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-23b">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-23c">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-23d">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-23e">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-247">
         <name>arm_offset_f32</name>
         <value>0x5863</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-250">
         <name>arm_mean_f32</name>
         <value>0x5cbd</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-259">
         <name>arm_rms_f32</name>
         <value>0x5791</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-264">
         <name>DL_ADC12_setClockConfig</name>
         <value>0x6461</value>
         <object_component_ref idref="oc-1b2"/>
      </symbol>
      <symbol id="sm-26d">
         <name>DL_Common_delayCycles</name>
         <value>0x5fc7</value>
         <object_component_ref idref="oc-110"/>
      </symbol>
      <symbol id="sm-277">
         <name>DL_DMA_initChannel</name>
         <value>0x6269</value>
         <object_component_ref idref="oc-20e"/>
      </symbol>
      <symbol id="sm-284">
         <name>DL_SPI_init</name>
         <value>0x6395</value>
         <object_component_ref idref="oc-1ac"/>
      </symbol>
      <symbol id="sm-285">
         <name>DL_SPI_setClockConfig</name>
         <value>0x6fff</value>
         <object_component_ref idref="oc-1a6"/>
      </symbol>
      <symbol id="sm-295">
         <name>DL_Timer_setClockConfig</name>
         <value>0x6c89</value>
         <object_component_ref idref="oc-18e"/>
      </symbol>
      <symbol id="sm-296">
         <name>DL_Timer_initTimerMode</name>
         <value>0x54ed</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-2a3">
         <name>DL_UART_init</name>
         <value>0x634d</value>
         <object_component_ref idref="oc-1a0"/>
      </symbol>
      <symbol id="sm-2a4">
         <name>DL_UART_setClockConfig</name>
         <value>0x7011</value>
         <object_component_ref idref="oc-19a"/>
      </symbol>
      <symbol id="sm-2af">
         <name>DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <value>0x6045</value>
         <object_component_ref idref="oc-188"/>
      </symbol>
      <symbol id="sm-2b9">
         <name>sqrtf</name>
         <value>0x5a69</value>
         <object_component_ref idref="oc-152"/>
      </symbol>
      <symbol id="sm-2c4">
         <name>__aeabi_errno_addr</name>
         <value>0x70a9</value>
         <object_component_ref idref="oc-1ee"/>
      </symbol>
      <symbol id="sm-2c5">
         <name>__aeabi_errno</name>
         <value>0x202031e4</value>
         <object_component_ref idref="oc-21b"/>
      </symbol>
      <symbol id="sm-2d3">
         <name>_c_int00_noargs</name>
         <value>0x6a69</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-2d4">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-2e0">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x6655</value>
         <object_component_ref idref="oc-e1"/>
      </symbol>
      <symbol id="sm-2e8">
         <name>_system_pre_init</name>
         <value>0x70cb</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-2f3">
         <name>__TI_zero_init</name>
         <value>0x7059</value>
         <object_component_ref idref="oc-52"/>
      </symbol>
      <symbol id="sm-2fc">
         <name>__TI_decompress_none</name>
         <value>0x7035</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-307">
         <name>__TI_decompress_lzss</name>
         <value>0x5ed1</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-316">
         <name>abort</name>
         <value>0x70b9</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-32a">
         <name>HOSTexit</name>
         <value>0x70c3</value>
         <object_component_ref idref="oc-163"/>
      </symbol>
      <symbol id="sm-32b">
         <name>C$$EXIT</name>
         <value>0x70c2</value>
         <object_component_ref idref="oc-163"/>
      </symbol>
      <symbol id="sm-340">
         <name>__aeabi_fadd</name>
         <value>0x56c3</value>
         <object_component_ref idref="oc-144"/>
      </symbol>
      <symbol id="sm-341">
         <name>__addsf3</name>
         <value>0x56c3</value>
         <object_component_ref idref="oc-144"/>
      </symbol>
      <symbol id="sm-342">
         <name>__aeabi_fsub</name>
         <value>0x56b9</value>
         <object_component_ref idref="oc-144"/>
      </symbol>
      <symbol id="sm-343">
         <name>__subsf3</name>
         <value>0x56b9</value>
         <object_component_ref idref="oc-144"/>
      </symbol>
      <symbol id="sm-349">
         <name>__aeabi_dadd</name>
         <value>0x4df3</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-34a">
         <name>__adddf3</name>
         <value>0x4df3</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-34b">
         <name>__aeabi_dsub</name>
         <value>0x4de9</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-34c">
         <name>__subdf3</name>
         <value>0x4de9</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-352">
         <name>__aeabi_dmul</name>
         <value>0x55d5</value>
         <object_component_ref idref="oc-126"/>
      </symbol>
      <symbol id="sm-353">
         <name>__muldf3</name>
         <value>0x55d5</value>
         <object_component_ref idref="oc-126"/>
      </symbol>
      <symbol id="sm-359">
         <name>__muldsi3</name>
         <value>0x66cd</value>
         <object_component_ref idref="oc-159"/>
      </symbol>
      <symbol id="sm-35f">
         <name>__aeabi_fmul</name>
         <value>0x5c31</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-360">
         <name>__mulsf3</name>
         <value>0x5c31</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-366">
         <name>__aeabi_fdiv</name>
         <value>0x5e4d</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-367">
         <name>__divsf3</name>
         <value>0x5e4d</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-36d">
         <name>__aeabi_ddiv</name>
         <value>0x52d9</value>
         <object_component_ref idref="oc-11a"/>
      </symbol>
      <symbol id="sm-36e">
         <name>__divdf3</name>
         <value>0x52d9</value>
         <object_component_ref idref="oc-11a"/>
      </symbol>
      <symbol id="sm-374">
         <name>__aeabi_f2d</name>
         <value>0x6561</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-375">
         <name>__extendsfdf2</name>
         <value>0x6561</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-37b">
         <name>__aeabi_f2iz</name>
         <value>0x6741</value>
         <object_component_ref idref="oc-cf"/>
      </symbol>
      <symbol id="sm-37c">
         <name>__fixsfsi</name>
         <value>0x6741</value>
         <object_component_ref idref="oc-cf"/>
      </symbol>
      <symbol id="sm-382">
         <name>__aeabi_d2uiz</name>
         <value>0x641d</value>
         <object_component_ref idref="oc-12e"/>
      </symbol>
      <symbol id="sm-383">
         <name>__fixunsdfsi</name>
         <value>0x641d</value>
         <object_component_ref idref="oc-12e"/>
      </symbol>
      <symbol id="sm-389">
         <name>__aeabi_i2f</name>
         <value>0x65a1</value>
         <object_component_ref idref="oc-cb"/>
      </symbol>
      <symbol id="sm-38a">
         <name>__floatsisf</name>
         <value>0x65a1</value>
         <object_component_ref idref="oc-cb"/>
      </symbol>
      <symbol id="sm-390">
         <name>__aeabi_ui2d</name>
         <value>0x6b01</value>
         <object_component_ref idref="oc-116"/>
      </symbol>
      <symbol id="sm-391">
         <name>__floatunsidf</name>
         <value>0x6b01</value>
         <object_component_ref idref="oc-116"/>
      </symbol>
      <symbol id="sm-397">
         <name>__aeabi_ui2f</name>
         <value>0x6a41</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-398">
         <name>__floatunsisf</name>
         <value>0x6a41</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-39f">
         <name>__aeabi_d2f</name>
         <value>0x5fd1</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-3a0">
         <name>__truncdfsf2</name>
         <value>0x5fd1</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-3a6">
         <name>__aeabi_fcmpeq</name>
         <value>0x610d</value>
         <object_component_ref idref="oc-14e"/>
      </symbol>
      <symbol id="sm-3a7">
         <name>__aeabi_fcmplt</name>
         <value>0x6121</value>
         <object_component_ref idref="oc-14e"/>
      </symbol>
      <symbol id="sm-3a8">
         <name>__aeabi_fcmple</name>
         <value>0x6135</value>
         <object_component_ref idref="oc-14e"/>
      </symbol>
      <symbol id="sm-3a9">
         <name>__aeabi_fcmpge</name>
         <value>0x6149</value>
         <object_component_ref idref="oc-14e"/>
      </symbol>
      <symbol id="sm-3aa">
         <name>__aeabi_fcmpgt</name>
         <value>0x615d</value>
         <object_component_ref idref="oc-14e"/>
      </symbol>
      <symbol id="sm-3b0">
         <name>__aeabi_memcpy</name>
         <value>0x70b1</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-3b1">
         <name>__aeabi_memcpy4</name>
         <value>0x70b1</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-3b2">
         <name>__aeabi_memcpy8</name>
         <value>0x70b1</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-3b9">
         <name>__aeabi_memclr</name>
         <value>0x7091</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-3ba">
         <name>__aeabi_memclr4</name>
         <value>0x7091</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-3bb">
         <name>__aeabi_memclr8</name>
         <value>0x7091</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-3c1">
         <name>__aeabi_uidiv</name>
         <value>0x6521</value>
         <object_component_ref idref="oc-140"/>
      </symbol>
      <symbol id="sm-3c2">
         <name>__aeabi_uidivmod</name>
         <value>0x6521</value>
         <object_component_ref idref="oc-140"/>
      </symbol>
      <symbol id="sm-3cb">
         <name>__eqsf2</name>
         <value>0x6691</value>
         <object_component_ref idref="oc-1e7"/>
      </symbol>
      <symbol id="sm-3cc">
         <name>__lesf2</name>
         <value>0x6691</value>
         <object_component_ref idref="oc-1e7"/>
      </symbol>
      <symbol id="sm-3cd">
         <name>__ltsf2</name>
         <value>0x6691</value>
         <object_component_ref idref="oc-1e7"/>
      </symbol>
      <symbol id="sm-3ce">
         <name>__nesf2</name>
         <value>0x6691</value>
         <object_component_ref idref="oc-1e7"/>
      </symbol>
      <symbol id="sm-3cf">
         <name>__cmpsf2</name>
         <value>0x6691</value>
         <object_component_ref idref="oc-1e7"/>
      </symbol>
      <symbol id="sm-3d0">
         <name>__gtsf2</name>
         <value>0x65dd</value>
         <object_component_ref idref="oc-1ec"/>
      </symbol>
      <symbol id="sm-3d1">
         <name>__gesf2</name>
         <value>0x65dd</value>
         <object_component_ref idref="oc-1ec"/>
      </symbol>
      <symbol id="sm-3dc">
         <name>__aeabi_idiv0</name>
         <value>0x50a3</value>
         <object_component_ref idref="oc-1e0"/>
      </symbol>
      <symbol id="sm-3e6">
         <name>TI_memcpy_small</name>
         <value>0x7023</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-3ef">
         <name>TI_memset_small</name>
         <value>0x7069</value>
         <object_component_ref idref="oc-f4"/>
      </symbol>
      <symbol id="sm-3f0">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-3f3">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-3f4">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
