******************************************************************************
            TI ARM Clang Linker Unix v4.0.2                    
******************************************************************************
>> Linked Tue Jul 22 17:57:40 2025

OUTPUT FILE NAME:   <0.96lcd copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00006ac5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006b70  00019490  R  X
  SRAM                  20200000   00008000  00000228  00007dd8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003a48   00003a48    r--
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003988   00003988    r-- .rodata
00003a50    00003a50    00003128   00003128    r-x
  00003a50    00003a50    00003110   00003110    r-x .text
  00006b60    00006b60    00000018   00000018    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    00003a50    00003110     
                  00003a50    00000faa     lcd_init.o (.text.LCD_Init)
                  000049fa    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000049fc    00000400     lcd.o (.text.LCD_ShowChar)
                  00004dfc    000002c0     lcd.o (.text.LCD_Fill)
                  000050bc    0000027c     lcd.o (.text.LCD_ShowChinese12x12)
                  00005338    0000027c     lcd.o (.text.LCD_ShowChinese16x16)
                  000055b4    0000027c     lcd.o (.text.LCD_ShowChinese24x24)
                  00005830    0000027c     lcd.o (.text.LCD_ShowChinese32x32)
                  00005aac    00000278     empty.o (.text.main)
                  00005d24    0000022c     lcd_init.o (.text.LCD_Address_Set)
                  00005f50    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000060e2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000060e4    00000134     lcd.o (.text.LCD_ShowFloatNum1)
                  00006218    0000010a     lcd.o (.text.LCD_ShowIntNum)
                  00006322    00000002     --HOLE-- [fill = 0]
                  00006324    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00006400    000000b2     lcd.o (.text.LCD_ShowChinese)
                  000064b2    00000002     --HOLE-- [fill = 0]
                  000064b4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00006540    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000065b4    00000070     lcd_init.o (.text.LCD_WR_DATA)
                  00006624    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00006688    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000066ec    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  0000674c    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000679c    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_LCD_init)
                  000067e8    0000004a     lcd.o (.text.LCD_ShowString)
                  00006832    00000002     --HOLE-- [fill = 0]
                  00006834    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000687c    00000044                 : dl_spi.o (.text.DL_SPI_init)
                  000068c0    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006904    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00006944    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006984    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000069c4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006a00    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00006a3a    00000002     --HOLE-- [fill = 0]
                  00006a3c    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00006a74    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_CLK_init)
                  00006a9c    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006ac4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00006aec    00000016            : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00006b02    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00006b14    00000012                 : dl_uart.o (.text.DL_UART_setClockConfig)
                  00006b26    00000002     --HOLE-- [fill = 0]
                  00006b28    00000010     board.o (.text.delay_ms)
                  00006b38    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00006b42    00000006     libc.a : exit.c.obj (.text:abort)
                  00006b48    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00006b4c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00006b50    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00006b54    0000000c     --HOLE-- [fill = 0]

.cinit     0    00006b60    00000018     
                  00006b60    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006b68    00000004     (__TI_handler_table)
                  00006b6c    00000008     (__TI_cinit_table)
                  00006b74    00000004     --HOLE-- [fill = 0]

.rodata    0    000000c0    00003988     
                  000000c0    000017c0     lcd.o (.rodata.ascii_3216)
                  00001880    000011d0     lcd.o (.rodata.ascii_2412)
                  00002a50    000005f0     lcd.o (.rodata.ascii_1608)
                  00003040    00000474     lcd.o (.rodata.ascii_1206)
                  000034b4    0000028a     lcd.o (.rodata.tfont32)
                  0000373e    00000172     lcd.o (.rodata.tfont24)
                  000038b0    000000aa     lcd.o (.rodata.tfont16)
                  0000395a    00000087     lcd.o (.rodata.tfont12)
                  000039e1    00000002     ti_msp_dl_config.o (.rodata.gSPI_LCD_clockConfig)
                  000039e3    00000001     --HOLE-- [fill = 0]
                  000039e4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00003a0c    00000010     empty.o (.rodata.str1.9517790425240694019.1)
                  00003a1c    0000000a     ti_msp_dl_config.o (.rodata.gSPI_LCD_config)
                  00003a26    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00003a30    00000007     empty.o (.rodata.str1.14685083708502177989.1)
                  00003a37    00000007     empty.o (.rodata.str1.254342170260855183.1)
                  00003a3e    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00003a40    00000002     empty.o (.rodata.str1.11898133897667081452.1)
                  00003a42    00000002     empty.o (.rodata.str1.16704889451495720520.1)
                  00003a44    00000002     empty.o (.rodata.str1.17669528882079347314.1)
                  00003a46    00000002     empty.o (.rodata.str1.7401042497206923953.1)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_LCDBackup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    ./
       empty.o                        632     38        0      
       ti_msp_dl_config.o             496     64        40     
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1134    294       40     
                                                               
    ./BSP/LCD/
       lcd.o                          5098    14625     0      
       lcd_init.o                     4678    0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         9776    14625     0      
                                                               
    ./Board/
       board.o                        16      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         16      0         0      
                                                               
    /home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_zero_init.c.obj           22      0         0      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         132     0         0      
                                                               
    /home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    /home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402     0         0      
       mulsf3.S.obj                   140     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         902     0         0      
                                                               
    /home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         574     0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       20        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   12538   14939     552    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006b6c records: 1, size/record: 8, table size: 8
	.bss: load addr=00006b60, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006b68 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000049fb  ADC0_IRQHandler                      
000049fb  ADC1_IRQHandler                      
000049fb  AES_IRQHandler                       
00006b48  C$$EXIT                              
000049fb  CANFD0_IRQHandler                    
000049fb  DAC0_IRQHandler                      
00006b39  DL_Common_delayCycles                
0000687d  DL_SPI_init                          
00006b03  DL_SPI_setClockConfig                
00006325  DL_SYSCTL_configSYSPLL               
00006625  DL_SYSCTL_setHFCLKSourceHFXTParams   
000068c1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006835  DL_UART_init                         
00006b15  DL_UART_setClockConfig               
000049fb  DMA_IRQHandler                       
000049fb  Default_Handler                      
000049fb  GROUP0_IRQHandler                    
000049fb  GROUP1_IRQHandler                    
00006b49  HOSTexit                             
000049fb  HardFault_Handler                    
000049fb  I2C0_IRQHandler                      
000049fb  I2C1_IRQHandler                      
00005d25  LCD_Address_Set                      
00004dfd  LCD_Fill                             
00003a51  LCD_Init                             
000049fd  LCD_ShowChar                         
00006401  LCD_ShowChinese                      
000050bd  LCD_ShowChinese12x12                 
00005339  LCD_ShowChinese16x16                 
000055b5  LCD_ShowChinese24x24                 
00005831  LCD_ShowChinese32x32                 
000060e5  LCD_ShowFloatNum1                    
00006219  LCD_ShowIntNum                       
000067e9  LCD_ShowString                       
000065b5  LCD_WR_DATA                          
000049fb  NMI_Handler                          
000049fb  PendSV_Handler                       
000049fb  RTC_IRQHandler                       
00006b4d  Reset_Handler                        
000049fb  SPI0_IRQHandler                      
000049fb  SPI1_IRQHandler                      
000049fb  SVC_Handler                          
0000674d  SYSCFG_DL_GPIO_init                  
0000679d  SYSCFG_DL_SPI_LCD_init               
00006a75  SYSCFG_DL_SYSCTL_CLK_init            
00006689  SYSCFG_DL_SYSCTL_init                
000066ed  SYSCFG_DL_UART_0_init                
00006a9d  SYSCFG_DL_init                       
00006905  SYSCFG_DL_initPower                  
000049fb  SysTick_Handler                      
000049fb  TIMA0_IRQHandler                     
000049fb  TIMA1_IRQHandler                     
000049fb  TIMG0_IRQHandler                     
000049fb  TIMG12_IRQHandler                    
000049fb  TIMG6_IRQHandler                     
000049fb  TIMG7_IRQHandler                     
000049fb  TIMG8_IRQHandler                     
000049fb  UART0_IRQHandler                     
000049fb  UART1_IRQHandler                     
000049fb  UART2_IRQHandler                     
000049fb  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006b6c  __TI_CINIT_Base                      
00006b74  __TI_CINIT_Limit                     
00006b74  __TI_CINIT_Warm                      
00006b68  __TI_Handler_Table_Base              
00006b6c  __TI_Handler_Table_Limit             
000069c5  __TI_auto_init_nobinit_nopinit       
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00006aed  __TI_zero_init_nomemset              
00005f5b  __adddf3                             
00006541  __aeabi_d2f                          
00005f5b  __aeabi_dadd                         
00005f51  __aeabi_dsub                         
00006985  __aeabi_f2d                          
00006a3d  __aeabi_f2iz                         
000064b5  __aeabi_fmul                         
000060e3  __aeabi_idiv0                        
00006945  __aeabi_uidiv                        
00006945  __aeabi_uidivmod                     
ffffffff  __binit__                            
00006985  __extendsfdf2                        
00006a3d  __fixsfsi                            
UNDEFED   __mpu_init                           
00006a01  __muldsi3                            
000064b5  __mulsf3                             
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00005f51  __subdf3                             
00006541  __truncdfsf2                         
00006ac5  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00006b51  _system_pre_init                     
00006b43  abort                                
00003040  ascii_1206                           
00002a50  ascii_1608                           
00001880  ascii_2412                           
000000c0  ascii_3216                           
ffffffff  binit                                
00006b29  delay_ms                             
20200000  gSPI_LCDBackup                       
00000000  interruptVectors                     
00005aad  main                                 
0000395a  tfont12                              
000038b0  tfont16                              
0000373e  tfont24                              
000034b4  tfont32                              


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c0  ascii_3216                           
00000200  __STACK_SIZE                         
00001880  ascii_2412                           
00002a50  ascii_1608                           
00003040  ascii_1206                           
000034b4  tfont32                              
0000373e  tfont24                              
000038b0  tfont16                              
0000395a  tfont12                              
00003a51  LCD_Init                             
000049fb  ADC0_IRQHandler                      
000049fb  ADC1_IRQHandler                      
000049fb  AES_IRQHandler                       
000049fb  CANFD0_IRQHandler                    
000049fb  DAC0_IRQHandler                      
000049fb  DMA_IRQHandler                       
000049fb  Default_Handler                      
000049fb  GROUP0_IRQHandler                    
000049fb  GROUP1_IRQHandler                    
000049fb  HardFault_Handler                    
000049fb  I2C0_IRQHandler                      
000049fb  I2C1_IRQHandler                      
000049fb  NMI_Handler                          
000049fb  PendSV_Handler                       
000049fb  RTC_IRQHandler                       
000049fb  SPI0_IRQHandler                      
000049fb  SPI1_IRQHandler                      
000049fb  SVC_Handler                          
000049fb  SysTick_Handler                      
000049fb  TIMA0_IRQHandler                     
000049fb  TIMA1_IRQHandler                     
000049fb  TIMG0_IRQHandler                     
000049fb  TIMG12_IRQHandler                    
000049fb  TIMG6_IRQHandler                     
000049fb  TIMG7_IRQHandler                     
000049fb  TIMG8_IRQHandler                     
000049fb  UART0_IRQHandler                     
000049fb  UART1_IRQHandler                     
000049fb  UART2_IRQHandler                     
000049fb  UART3_IRQHandler                     
000049fd  LCD_ShowChar                         
00004dfd  LCD_Fill                             
000050bd  LCD_ShowChinese12x12                 
00005339  LCD_ShowChinese16x16                 
000055b5  LCD_ShowChinese24x24                 
00005831  LCD_ShowChinese32x32                 
00005aad  main                                 
00005d25  LCD_Address_Set                      
00005f51  __aeabi_dsub                         
00005f51  __subdf3                             
00005f5b  __adddf3                             
00005f5b  __aeabi_dadd                         
000060e3  __aeabi_idiv0                        
000060e5  LCD_ShowFloatNum1                    
00006219  LCD_ShowIntNum                       
00006325  DL_SYSCTL_configSYSPLL               
00006401  LCD_ShowChinese                      
000064b5  __aeabi_fmul                         
000064b5  __mulsf3                             
00006541  __aeabi_d2f                          
00006541  __truncdfsf2                         
000065b5  LCD_WR_DATA                          
00006625  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006689  SYSCFG_DL_SYSCTL_init                
000066ed  SYSCFG_DL_UART_0_init                
0000674d  SYSCFG_DL_GPIO_init                  
0000679d  SYSCFG_DL_SPI_LCD_init               
000067e9  LCD_ShowString                       
00006835  DL_UART_init                         
0000687d  DL_SPI_init                          
000068c1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006905  SYSCFG_DL_initPower                  
00006945  __aeabi_uidiv                        
00006945  __aeabi_uidivmod                     
00006985  __aeabi_f2d                          
00006985  __extendsfdf2                        
000069c5  __TI_auto_init_nobinit_nopinit       
00006a01  __muldsi3                            
00006a3d  __aeabi_f2iz                         
00006a3d  __fixsfsi                            
00006a75  SYSCFG_DL_SYSCTL_CLK_init            
00006a9d  SYSCFG_DL_init                       
00006ac5  _c_int00_noargs                      
00006aed  __TI_zero_init_nomemset              
00006b03  DL_SPI_setClockConfig                
00006b15  DL_UART_setClockConfig               
00006b29  delay_ms                             
00006b39  DL_Common_delayCycles                
00006b43  abort                                
00006b48  C$$EXIT                              
00006b49  HOSTexit                             
00006b4d  Reset_Handler                        
00006b51  _system_pre_init                     
00006b68  __TI_Handler_Table_Base              
00006b6c  __TI_CINIT_Base                      
00006b6c  __TI_Handler_Table_Limit             
00006b74  __TI_CINIT_Limit                     
00006b74  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  gSPI_LCDBackup                       
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[123 symbols]
