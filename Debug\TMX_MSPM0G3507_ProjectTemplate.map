******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Nov 13 18:52:21 2024

OUTPUT FILE NAME:   <TMX_MSPM0G3507_ProjectTemplate.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004ded


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009880  00016780  R  X
  SRAM                  20200000   00008000  0000022c  00007dd4  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009880   00009880    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005010   00005010    r-x .text
  000050d0    000050d0    00004780   00004780    r-- .rodata
  00009850    00009850    00000030   00000030    r-- .cinit
20200000    20200000    0000002c   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
  20200028    20200028    00000004   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005010     
                  000000c0    00001074     lcd_init.o (.text.LCD_Init)
                  00001134    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00001b04    00000400     lcd.o (.text.LCD_ShowChar)
                  00001f04    000002c0     lcd.o (.text.LCD_Fill)
                  000021c4    0000027c     lcd.o (.text.LCD_ShowChinese12x12)
                  00002440    0000027c     lcd.o (.text.LCD_ShowChinese16x16)
                  000026bc    0000027c     lcd.o (.text.LCD_ShowChinese24x24)
                  00002938    0000027c     lcd.o (.text.LCD_ShowChinese32x32)
                  00002bb4    00000234     lcd_init.o (.text.LCD_Address_Set)
                  00002de8    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00003008    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000031e4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00003376    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003378    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000034b4    00000134     lcd.o (.text.LCD_ShowFloatNum1)
                  000035e8    00000128     empty.o (.text.main)
                  00003710    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003830    0000010c     board.o (.text.LOG_Debug_Out)
                  0000393c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003a48    0000010a     lcd.o (.text.LCD_ShowIntNum)
                  00003b52    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00003b54    000000ec     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003c40    000000ec     lcd.o (.text.LCD_ShowPicture)
                  00003d2c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003e10    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003ee8    000000b2     lcd.o (.text.LCD_ShowChinese)
                  00003f9a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003f9c    000000a2                            : udivmoddi4.S.obj (.text)
                  0000403e    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  000040d8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00004164    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000041e0    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00004254    0000000c     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_getClockStatus)
                  00004260    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000042d4    00000070     lcd_init.o (.text.LCD_WR_DATA)
                  00004344    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000043ac    00000068     board.o (.text.lc_printf)
                  00004414    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000447a    00000002     --HOLE-- [fill = 0]
                  0000447c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000044e0    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004544    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000045a6    00000062     libc.a : memset16.S.obj (.text:memset)
                  00004608    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00004668    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000046c4    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  0000471c    00000058            : _printfi.c.obj (.text._pconv_f)
                  00004774    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000047ca    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000481c    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000486c    0000004c     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000048b8    0000004c                 : dl_uart.o (.text.DL_UART_init)
                  00004904    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_LCD_init)
                  00004950    0000004a     lcd.o (.text.LCD_ShowString)
                  0000499a    00000002     --HOLE-- [fill = 0]
                  0000499c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000049e6    00000002     --HOLE-- [fill = 0]
                  000049e8    00000048     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00004a30    00000040     lcd_init.o (.text.LCD_WR_DATA8)
                  00004a70    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00004ab0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004af0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004b30    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004b70    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00004bb0    0000003c            : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004bec    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00004c26    00000002     --HOLE-- [fill = 0]
                  00004c28    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00004c60    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004c98    00000030            : _printfi.c.obj (.text._fcpy)
                  00004cc8    00000030            : vsnprintf.c.obj (.text._outs)
                  00004cf8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00004d24    00000028     driverlib.a : dl_spi.o (.text.DL_Common_updateReg)
                  00004d4c    00000028                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_Common_updateReg)
                  00004d74    00000028                 : dl_uart.o (.text.DL_Common_updateReg)
                  00004d9c    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_CLK_init)
                  00004dc4    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004dec    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004e14    00000024     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00004e38    00000024                 : dl_uart.o (.text.DL_UART_setClockConfig)
                  00004e5c    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00004e80    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004ea2    00000002     --HOLE-- [fill = 0]
                  00004ea4    00000020     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_CORE_configInstruction)
                  00004ec4    00000020                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFXTFrequencyRange)
                  00004ee4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004f02    00000002     --HOLE-- [fill = 0]
                  00004f04    00000018     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFXTStartupTime)
                  00004f1c    00000018                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHSCLKSource)
                  00004f34    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00004f4c    00000018            : sprintf.c.obj (.text._outs)
                  00004f64    00000016     driverlib.a : dl_uart.o (.text.DL_UART_disable)
                  00004f7a    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004f90    00000014     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004fa4    00000014                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_disableHFCLKStartupMonitor)
                  00004fb8    00000014                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004fcc    00000014                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_enableHFCLKStartupMonitor)
                  00004fe0    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004ff4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005008    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000501a    00000002     --HOLE-- [fill = 0]
                  0000501c    00000010     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_CORE_getInstructionConfig)
                  0000502c    00000010                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_disableHFXT)
                  0000503c    00000010     board.o (.text.delay_ms)
                  0000504c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  0000505c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000506a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005078    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005084    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000508e    00000002     --HOLE-- [fill = 0]
                  00005090    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000050a0    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000050aa    0000000a            : sprintf.c.obj (.text._outc)
                  000050b4    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000050bc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000050c4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000050c8    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000050cc    00000004            : exit.c.obj (.text:abort)

.cinit     0    00009850    00000030     
                  00009850    0000000c     (__TI_handler_table)
                  0000985c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009864    00000007     (.cinit..data.load) [load image, compression = lzss]
                  0000986b    00000001     --HOLE-- [fill = 0]
                  0000986c    00000010     (__TI_cinit_table)
                  0000987c    00000004     --HOLE-- [fill = 0]

.rodata    0    000050d0    00004780     
                  000050d0    000017c0     lcd.o (.rodata.ascii_3216)
                  00006890    000011d0     lcd.o (.rodata.ascii_2412)
                  00007a60    00000c80     empty.o (.rodata.gImage_1)
                  000086e0    000005f0     lcd.o (.rodata.ascii_1608)
                  00008cd0    00000474     lcd.o (.rodata.ascii_1206)
                  00009144    0000028a     lcd.o (.rodata.tfont32)
                  000093ce    00000172     lcd.o (.rodata.tfont24)
                  00009540    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009641    000000aa     lcd.o (.rodata.tfont16)
                  000096eb    00000087     lcd.o (.rodata.tfont12)
                  00009772    00000002     ti_msp_dl_config.o (.rodata.gSPI_LCD_clockConfig)
                  00009774    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000979c    00000016     board.o (.rodata.str1.129876060660167078031)
                  000097b2    00000015     empty.o (.rodata.str1.142855731982211788051)
                  000097c7    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  000097d8    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  000097e9    00000010     empty.o (.rodata.str1.147958455504881313061)
                  000097f9    0000000b     empty.o (.rodata.str1.176633223477948356601)
                  00009804    0000000b     empty.o (.rodata.str1.8167968867256355221)
                  0000980f    00000001     --HOLE-- [fill = 0]
                  00009810    0000000a     ti_msp_dl_config.o (.rodata.gSPI_LCD_config)
                  0000981a    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00009824    00000007     empty.o (.rodata.str1.158377844875001402571)
                  0000982b    00000007     empty.o (.rodata.str1.181099149860726468451)
                  00009832    00000006     empty.o (.rodata.str1.100506750686581518081)
                  00009838    00000006     empty.o (.rodata.str1.90710503391206826811)
                  0000983e    00000005     empty.o (.rodata.str1.104231497482819607001)
                  00009843    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00009845    0000000b     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_LCDBackup)

.data      0    20200028    00000004     UNINITIALIZED
                  20200028    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       empty.o                        296     3290      0      
       ti_msp_dl_config.o             496     64        40     
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         798     3546      40     
                                                               
    .\BSP\LCD\
       lcd.o                          5334    14625     0      
       lcd_init.o                     4952    0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         10286   14625     0      
                                                               
    .\Board\
       board.o                        388     22        0      
    +--+------------------------------+-------+---------+---------+
       Total:                         388     22        0      
                                                               
    C:/ti/mspm0_sdk_2_02_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   668     0         0      
       dl_uart.o                      174     0         0      
       dl_spi.o                       148     0         0      
       dl_common.o                    20      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1010    0         0      
                                                               
    F:\Ti\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       memcpy16.S.obj                 154     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5778    291       4      
                                                               
    F:\Ti\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2220    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       43        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   20480   18527     556    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000986c records: 2, size/record: 8, table size: 16
	.bss: load addr=0000985c, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init
	.data: load addr=00009864, load size=00000007 bytes, run addr=20200028, run size=00000004 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009850 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000031e5     00005090     0000508c   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003377  ADC0_IRQHandler                      
00003377  ADC1_IRQHandler                      
00003377  AES_IRQHandler                       
000050cc  C$$EXIT                              
00003377  CANFD0_IRQHandler                    
00003377  DAC0_IRQHandler                      
00004f91  DL_Common_delayCycles                
000049e9  DL_SPI_init                          
00004e15  DL_SPI_setClockConfig                
00003b55  DL_SYSCTL_configSYSPLL               
0000447d  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000486d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000048b9  DL_UART_init                         
00004e39  DL_UART_setClockConfig               
00003377  DMA_IRQHandler                       
00003377  Default_Handler                      
00003377  GROUP0_IRQHandler                    
00003377  GROUP1_IRQHandler                    
00003377  HardFault_Handler                    
00003377  I2C0_IRQHandler                      
00003377  I2C1_IRQHandler                      
00002bb5  LCD_Address_Set                      
00001f05  LCD_Fill                             
000000c1  LCD_Init                             
00001b05  LCD_ShowChar                         
00003ee9  LCD_ShowChinese                      
000021c5  LCD_ShowChinese12x12                 
00002441  LCD_ShowChinese16x16                 
000026bd  LCD_ShowChinese24x24                 
00002939  LCD_ShowChinese32x32                 
000034b5  LCD_ShowFloatNum1                    
00003a49  LCD_ShowIntNum                       
00003c41  LCD_ShowPicture                      
00004951  LCD_ShowString                       
000042d5  LCD_WR_DATA                          
00004a31  LCD_WR_DATA8                         
00003831  LOG_Debug_Out                        
00003377  NMI_Handler                          
00003377  PendSV_Handler                       
00003377  RTC_IRQHandler                       
000050c5  Reset_Handler                        
00003377  SPI0_IRQHandler                      
00003377  SPI1_IRQHandler                      
00003377  SVC_Handler                          
0000481d  SYSCFG_DL_GPIO_init                  
00004905  SYSCFG_DL_SPI_LCD_init               
00004d9d  SYSCFG_DL_SYSCTL_CLK_init            
000044e1  SYSCFG_DL_SYSCTL_init                
00004609  SYSCFG_DL_UART_0_init                
00004dc5  SYSCFG_DL_init                       
00004a71  SYSCFG_DL_initPower                  
00003377  SysTick_Handler                      
00003377  TIMA0_IRQHandler                     
00003377  TIMA1_IRQHandler                     
00003377  TIMG0_IRQHandler                     
00003377  TIMG12_IRQHandler                    
00003377  TIMG6_IRQHandler                     
00003377  TIMG7_IRQHandler                     
00003377  TIMG8_IRQHandler                     
00003377  UART0_IRQHandler                     
00003377  UART1_IRQHandler                     
00003377  UART2_IRQHandler                     
00003377  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000986c  __TI_CINIT_Base                      
0000987c  __TI_CINIT_Limit                     
0000987c  __TI_CINIT_Warm                      
00009850  __TI_Handler_Table_Base              
0000985c  __TI_Handler_Table_Limit             
00004bb1  __TI_auto_init_nobinit_nopinit       
00004165  __TI_decompress_lzss                 
00005009  __TI_decompress_none                 
000046c5  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
00001135  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004f7b  __TI_zero_init_nomemset              
000031ef  __adddf3                             
00009540  __aeabi_ctype_table_                 
00009540  __aeabi_ctype_table_C                
00004261  __aeabi_d2f                          
0000499d  __aeabi_d2iz                         
000031ef  __aeabi_dadd                         
00004545  __aeabi_dcmpeq                       
00004581  __aeabi_dcmpge                       
00004595  __aeabi_dcmpgt                       
0000456d  __aeabi_dcmple                       
00004559  __aeabi_dcmplt                       
0000393d  __aeabi_ddiv                         
00003d2d  __aeabi_dmul                         
000031e5  __aeabi_dsub                         
20200028  __aeabi_errno                        
000050b5  __aeabi_errno_addr                   
00004af1  __aeabi_f2d                          
00004c29  __aeabi_f2iz                         
000040d9  __aeabi_fmul                         
00004cf9  __aeabi_i2d                          
00004775  __aeabi_idiv                         
00003b53  __aeabi_idiv0                        
00004775  __aeabi_idivmod                      
00003f9b  __aeabi_ldiv0                        
00004ee5  __aeabi_llsl                         
00004e5d  __aeabi_lmul                         
00005079  __aeabi_memclr                       
00005079  __aeabi_memclr4                      
00005079  __aeabi_memclr8                      
000050bd  __aeabi_memcpy                       
000050bd  __aeabi_memcpy4                      
000050bd  __aeabi_memcpy8                      
0000505d  __aeabi_memset                       
0000505d  __aeabi_memset4                      
0000505d  __aeabi_memset8                      
00004ab1  __aeabi_uidiv                        
00004ab1  __aeabi_uidivmod                     
00004fe1  __aeabi_uldivmod                     
00004ee5  __ashldi3                            
ffffffff  __binit__                            
00004345  __cmpdf2                             
0000393d  __divdf3                             
00004345  __eqdf2                              
00004af1  __extendsfdf2                        
0000499d  __fixdfsi                            
00004c29  __fixsfsi                            
00004cf9  __floatsidf                          
000041e1  __gedf2                              
000041e1  __gtdf2                              
00004345  __ledf2                              
00004345  __ltdf2                              
UNDEFED   __mpu_init                           
00003d2d  __muldf3                             
00004e5d  __muldi3                             
00004bed  __muldsi3                            
000040d9  __mulsf3                             
00004345  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000031e5  __subdf3                             
00004261  __truncdfsf2                         
00003f9d  __udivmoddi4                         
00004ded  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000050c9  _system_pre_init                     
000050cd  abort                                
00008cd0  ascii_1206                           
000086e0  ascii_1608                           
00006890  ascii_2412                           
000050d0  ascii_3216                           
00004b31  atoi                                 
ffffffff  binit                                
0000503d  delay_ms                             
00004669  frexp                                
00004669  frexpl                               
00007a60  gImage_1                             
20200000  gSPI_LCDBackup                       
00000000  interruptVectors                     
000043ad  lc_printf                            
00003e11  ldexp                                
00003e11  ldexpl                               
000035e9  main                                 
00004e81  memccpy                              
0000403f  memcpy                               
000045a7  memset                               
00003e11  scalbn                               
00003e11  scalbnl                              
00004c61  sprintf                              
000096eb  tfont12                              
00009641  tfont16                              
000093ce  tfont24                              
00009144  tfont32                              
00004b71  vsnprintf                            
0000504d  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  LCD_Init                             
00000200  __STACK_SIZE                         
00001135  __TI_printfi                         
00001b05  LCD_ShowChar                         
00001f05  LCD_Fill                             
000021c5  LCD_ShowChinese12x12                 
00002441  LCD_ShowChinese16x16                 
000026bd  LCD_ShowChinese24x24                 
00002939  LCD_ShowChinese32x32                 
00002bb5  LCD_Address_Set                      
000031e5  __aeabi_dsub                         
000031e5  __subdf3                             
000031ef  __adddf3                             
000031ef  __aeabi_dadd                         
00003377  ADC0_IRQHandler                      
00003377  ADC1_IRQHandler                      
00003377  AES_IRQHandler                       
00003377  CANFD0_IRQHandler                    
00003377  DAC0_IRQHandler                      
00003377  DMA_IRQHandler                       
00003377  Default_Handler                      
00003377  GROUP0_IRQHandler                    
00003377  GROUP1_IRQHandler                    
00003377  HardFault_Handler                    
00003377  I2C0_IRQHandler                      
00003377  I2C1_IRQHandler                      
00003377  NMI_Handler                          
00003377  PendSV_Handler                       
00003377  RTC_IRQHandler                       
00003377  SPI0_IRQHandler                      
00003377  SPI1_IRQHandler                      
00003377  SVC_Handler                          
00003377  SysTick_Handler                      
00003377  TIMA0_IRQHandler                     
00003377  TIMA1_IRQHandler                     
00003377  TIMG0_IRQHandler                     
00003377  TIMG12_IRQHandler                    
00003377  TIMG6_IRQHandler                     
00003377  TIMG7_IRQHandler                     
00003377  TIMG8_IRQHandler                     
00003377  UART0_IRQHandler                     
00003377  UART1_IRQHandler                     
00003377  UART2_IRQHandler                     
00003377  UART3_IRQHandler                     
000034b5  LCD_ShowFloatNum1                    
000035e9  main                                 
00003831  LOG_Debug_Out                        
0000393d  __aeabi_ddiv                         
0000393d  __divdf3                             
00003a49  LCD_ShowIntNum                       
00003b53  __aeabi_idiv0                        
00003b55  DL_SYSCTL_configSYSPLL               
00003c41  LCD_ShowPicture                      
00003d2d  __aeabi_dmul                         
00003d2d  __muldf3                             
00003e11  ldexp                                
00003e11  ldexpl                               
00003e11  scalbn                               
00003e11  scalbnl                              
00003ee9  LCD_ShowChinese                      
00003f9b  __aeabi_ldiv0                        
00003f9d  __udivmoddi4                         
0000403f  memcpy                               
000040d9  __aeabi_fmul                         
000040d9  __mulsf3                             
00004165  __TI_decompress_lzss                 
000041e1  __gedf2                              
000041e1  __gtdf2                              
00004261  __aeabi_d2f                          
00004261  __truncdfsf2                         
000042d5  LCD_WR_DATA                          
00004345  __cmpdf2                             
00004345  __eqdf2                              
00004345  __ledf2                              
00004345  __ltdf2                              
00004345  __nedf2                              
000043ad  lc_printf                            
0000447d  DL_SYSCTL_setHFCLKSourceHFXTParams   
000044e1  SYSCFG_DL_SYSCTL_init                
00004545  __aeabi_dcmpeq                       
00004559  __aeabi_dcmplt                       
0000456d  __aeabi_dcmple                       
00004581  __aeabi_dcmpge                       
00004595  __aeabi_dcmpgt                       
000045a7  memset                               
00004609  SYSCFG_DL_UART_0_init                
00004669  frexp                                
00004669  frexpl                               
000046c5  __TI_ltoa                            
00004775  __aeabi_idiv                         
00004775  __aeabi_idivmod                      
0000481d  SYSCFG_DL_GPIO_init                  
0000486d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000048b9  DL_UART_init                         
00004905  SYSCFG_DL_SPI_LCD_init               
00004951  LCD_ShowString                       
0000499d  __aeabi_d2iz                         
0000499d  __fixdfsi                            
000049e9  DL_SPI_init                          
00004a31  LCD_WR_DATA8                         
00004a71  SYSCFG_DL_initPower                  
00004ab1  __aeabi_uidiv                        
00004ab1  __aeabi_uidivmod                     
00004af1  __aeabi_f2d                          
00004af1  __extendsfdf2                        
00004b31  atoi                                 
00004b71  vsnprintf                            
00004bb1  __TI_auto_init_nobinit_nopinit       
00004bed  __muldsi3                            
00004c29  __aeabi_f2iz                         
00004c29  __fixsfsi                            
00004c61  sprintf                              
00004cf9  __aeabi_i2d                          
00004cf9  __floatsidf                          
00004d9d  SYSCFG_DL_SYSCTL_CLK_init            
00004dc5  SYSCFG_DL_init                       
00004ded  _c_int00_noargs                      
00004e15  DL_SPI_setClockConfig                
00004e39  DL_UART_setClockConfig               
00004e5d  __aeabi_lmul                         
00004e5d  __muldi3                             
00004e81  memccpy                              
00004ee5  __aeabi_llsl                         
00004ee5  __ashldi3                            
00004f7b  __TI_zero_init_nomemset              
00004f91  DL_Common_delayCycles                
00004fe1  __aeabi_uldivmod                     
00005009  __TI_decompress_none                 
0000503d  delay_ms                             
0000504d  wcslen                               
0000505d  __aeabi_memset                       
0000505d  __aeabi_memset4                      
0000505d  __aeabi_memset8                      
00005079  __aeabi_memclr                       
00005079  __aeabi_memclr4                      
00005079  __aeabi_memclr8                      
000050b5  __aeabi_errno_addr                   
000050bd  __aeabi_memcpy                       
000050bd  __aeabi_memcpy4                      
000050bd  __aeabi_memcpy8                      
000050c5  Reset_Handler                        
000050c9  _system_pre_init                     
000050cc  C$$EXIT                              
000050cd  abort                                
000050d0  ascii_3216                           
00006890  ascii_2412                           
00007a60  gImage_1                             
000086e0  ascii_1608                           
00008cd0  ascii_1206                           
00009144  tfont32                              
000093ce  tfont24                              
00009540  __aeabi_ctype_table_                 
00009540  __aeabi_ctype_table_C                
00009641  tfont16                              
000096eb  tfont12                              
00009850  __TI_Handler_Table_Base              
0000985c  __TI_Handler_Table_Limit             
0000986c  __TI_CINIT_Base                      
0000987c  __TI_CINIT_Limit                     
0000987c  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  gSPI_LCDBackup                       
20200028  __aeabi_errno                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[186 symbols]
