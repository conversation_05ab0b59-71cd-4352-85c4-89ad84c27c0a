#include "ti_msp_dl_config.h"
#include "board.h"
#include "stdio.h"
#include "lcd_init.h"
#include "lcd.h"
#include "pic.h"
#include "wave.h"
#include <math.h>
#include "arm_math.h"
#include "BSP/AD9850/ad9850.h"

// The GPIO definitions are now self-contained within ad9850.c,
// so they are no longer needed here.

// The callback and delay functions are also no longer needed
// as the new driver is self-contained.

uint16_t gADCSamples[1024] = {0};
int adc0_done = 0;
// --- 新增：为第二路ADC创建独立的数组和标志位 ---
uint16_t gADCSamples_ch1[1024] = {0};
int adc1_done = 0;


// 用于保存上一次绘制的波形点，以实现高效擦除
static Point g_prev_points[32];
// --- 新增：为第二路波形创建独立的上一帧坐标存储 ---
static Point g_prev_points_ch1[32];
static uint8_t g_has_prev_points = 0;

// --- 新增：用于动态控制AD9850频率 ---
static uint32_t g_current_freq_hz = 1000; // 初始频率 1kHz


int main(void)
{
    SYSCFG_DL_init();

    LCD_Init(); // LCD初始化

    // 初始化DDS模块
    dds_reset();
    dds_set(g_current_freq_hz); // Set initial frequency

  	// dma ch0 - Corrected to use DriverLib API
  	DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_0_INST, ADC12_0_ADCMEM_0));
  	DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gADCSamples[0]);
  	DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);

    // --- 新增：配置和启用DMA通道1，遵循您的代码风格 ---
    // Corrected to use DriverLib API
  	DL_DMA_setSrcAddr(DMA, DMA_CH1_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_1_INST, ADC12_1_ADCMEM_0));
  	DL_DMA_setDestAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&gADCSamples_ch1[0]);
  	DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);
    
    LCD_Fill(0,0,LCD_W,LCD_H,GREEN);
    delay_ms(100);
    
    LCD_ShowString(10, 5, (const u8 *)"Voltage", BLACK, GREEN, 16, 0);
    LCD_ShowString(10, 25, (const u8 *)"Current", BLACK, GREEN, 16, 0);
    LCD_ShowString(10, 45, (const u8 *)"Frequency", BLACK, GREEN, 16, 0);
    LCD_ShowString(150, 5, (const u8 *)"Active Power", BLACK, GREEN, 16, 0);
    LCD_ShowString(150, 25, (const u8 *)"Reactive Power", BLACK, GREEN, 16, 0);
    LCD_ShowString(150, 45, (const u8 *)"Power Factor", BLACK, GREEN, 16, 0);
	LCD_ShowChinese(100,220,(u8 *)"关注吸勾勾谢谢喵",YELLOW,GREEN,16,1);
      
    // Conversion factors based on 3.3V VREF and assumed hardware
    const float VREF = 3.3f; 
    const int ADC_MAX = 4095;
    
    // Voltage conversion: Direct connection, ratio is 1:1
    const float VOLTAGE_CONVERSION_FACTOR = (VREF / ADC_MAX) * 1.0f;
    
    // Current conversion: Direct connection, ratio is 1:1 (measures voltage, not amps)
    const float CURRENT_CONVERSION_FACTOR = (VREF / ADC_MAX) * 1.0f;

	NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);
    // --- 新增：启用ADC1的中断 ---
	NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);

  	DL_Timer_startCounter(TIMER_0_INST);

 	while(1)
 	{
		if (adc0_done == 1) {

            DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, 1024);
            // Corrected to use DriverLib API
            DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_0_INST, ADC12_0_ADCMEM_0));
            DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gADCSamples[0]);
            DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);
            adc0_done = 0;
        }

        // --- 新增：处理第二路ADC的数据 ---
		if (adc1_done == 1) {
            DL_DMA_setTransferSize(DMA, DMA_CH1_CHAN_ID, 1024);
            // Corrected to use DriverLib API
            DL_DMA_setSrcAddr(DMA, DMA_CH1_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_1_INST, ADC12_1_ADCMEM_0));
            DL_DMA_setDestAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&gADCSamples_ch1[0]);
            DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);
            adc1_done = 0;
        }
		
        // --- 使用 CMSIS-DSP 库计算 RMS (分解步骤) ---
        static float32_t float_samples_current[1024];
        static float32_t float_samples_voltage[1024];
        float32_t mean_current, mean_voltage;
        float32_t rms_adc_current, rms_adc_voltage;

        // 1. 将 uint16_t 采样数据转换为 float32_t
        for (int i = 0; i < 1024; i++) {
            float_samples_current[i] = (float32_t)gADCSamples[i];
            float_samples_voltage[i] = (float32_t)gADCSamples_ch1[i];
        }

        // 2. 计算直流偏置 (Mean)
        arm_mean_f32(float_samples_current, 1024, &mean_current);
        arm_mean_f32(float_samples_voltage, 1024, &mean_voltage);

        // 3. 移除直流偏置 (Offset)
        arm_offset_f32(float_samples_current, -mean_current, float_samples_current, 1024);
        arm_offset_f32(float_samples_voltage, -mean_voltage, float_samples_voltage, 1024);

        // 4. 计算处理后信号的 RMS
        arm_rms_f32(float_samples_current, 1024, &rms_adc_current);
        arm_rms_f32(float_samples_voltage, 1024, &rms_adc_voltage);

        float rms_current = rms_adc_current * CURRENT_CONVERSION_FACTOR;     // Blue waveform is Current
        float rms_voltage = rms_adc_voltage * VOLTAGE_CONVERSION_FACTOR; // Red waveform is Voltage

        LCD_ShowFloatNum1(85, 5, rms_voltage, 5, BLACK, GREEN, 16);
        LCD_ShowString(135, 5, (const u8 *)"V", BLACK, GREEN, 16, 0);

        LCD_ShowFloatNum1(85, 25, rms_current, 4, BLACK, GREEN, 16);
        LCD_ShowString(135, 25, (const u8 *)"A", BLACK, GREEN, 16, 0);
		
        // --- 新增：AD9850 频率动态控制与显示 ---
        // 1. 更新频率值 (从1kHz扫到50kHz，步进1kHz)
        g_current_freq_hz += 1000;
        if (g_current_freq_hz > 50000) {
            g_current_freq_hz = 1000;
        }

        // 2. 将新频率发送给 AD9850
        dds_set(g_current_freq_hz);

        // 3. 在LCD上显示当前频率 (单位: kHz)
        LCD_ShowFloatNum1(85, 45, (float)g_current_freq_hz / 1000.0f, 4, BLACK, GREEN, 16);
        LCD_ShowString(135, 45, (const u8 *)"kHz", BLACK, GREEN, 16, 0);

		// --- 扩展后的波形绘制逻辑：同时绘制两路波形 ---
		u16 x0 = 0;
		u16 y0 = 80;
		u16 width = 320;
		u16 height = 120;
		u16 data_len = 32;
		u16 color_ch0 = BLUE; // 通道0颜色
		u16 color_ch1 = RED;  // 通道1颜色
		u16 bgcolor = GREEN;

		// 1. 如果有上一帧的波形，用背景色重新绘制它，实现擦除效果
		if (g_has_prev_points) {
			for (int i = 0; i < data_len - 1; i++) {
				LCD_DrawLine(g_prev_points[i].x, g_prev_points[i].y, g_prev_points[i+1].x, g_prev_points[i+1].y, bgcolor);
                // --- 新增：擦除第二路波形 ---
				LCD_DrawLine(g_prev_points_ch1[i].x, g_prev_points_ch1[i].y, g_prev_points_ch1[i+1].x, g_prev_points_ch1[i+1].y, bgcolor);
			}
		}

		// 2. 计算当前帧两路数据点在屏幕上的坐标
		Point current_points[32];
        // --- 新增：为第二路波形创建独立的当前坐标存储 ---
		Point current_points_ch1[32];
		float x_step = (float)(width - 1) / (data_len - 1);
		float y_ratio = (float)(height - 1) / 4095.0f;

		for (int i = 0; i < data_len; i++) {
			// 计算X坐标 (两路共用)
			u16 current_x = x0 + (u16)(i * x_step);
			current_points[i].x = current_x;
            current_points_ch1[i].x = current_x; // X坐标相同

			// 计算Y坐标 - 通道0
			uint16_t raw_y_ch0 = (uint16_t)(gADCSamples[i] * y_ratio);
			if (raw_y_ch0 >= height) raw_y_ch0 = height - 1;
			current_points[i].y = y0 + (height - 1 - raw_y_ch0);

            // --- 新增：计算Y坐标 - 通道1 ---
			uint16_t raw_y_ch1 = (uint16_t)(gADCSamples_ch1[i] * y_ratio);
			if (raw_y_ch1 >= height) raw_y_ch1 = height - 1;
			current_points_ch1[i].y = y0 + (height - 1 - raw_y_ch1);
		}

		// 3. 绘制当前帧的波形
		for (int i = 0; i < data_len - 1; i++) {
			LCD_DrawLine(current_points[i].x, current_points[i].y, current_points[i+1].x, current_points[i+1].y, color_ch0);
            // --- 新增：绘制第二路波形 ---
			LCD_DrawLine(current_points_ch1[i].x, current_points_ch1[i].y, current_points_ch1[i+1].x, current_points_ch1[i+1].y, color_ch1);
		}

		// 4. 保存当前帧的坐标点，用于下一帧的擦除
		for (int i = 0; i < data_len; i++) {
			g_prev_points[i] = current_points[i];
            // --- 新增：保存第二路波形的坐标 ---
			g_prev_points_ch1[i] = current_points_ch1[i];
		}
		g_has_prev_points = 1;

        // Add a delay to quiet down the main loop, reducing digital noise
        delay_ms(100);
	}
}


void ADC12_0_INST_IRQHandler() {
  if (DL_ADC12_getPendingInterrupt(ADC12_0_INST) == DL_ADC12_IIDX_DMA_DONE) {
    adc0_done = 1;
  }
}

// --- 新增：ADC1的中断服务函数 ---
void ADC12_1_INST_IRQHandler() {
  if (DL_ADC12_getPendingInterrupt(ADC12_1_INST) == DL_ADC12_IIDX_DMA_DONE) {
    adc1_done = 1;
  }
}