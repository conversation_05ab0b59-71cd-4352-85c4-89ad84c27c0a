******************************************************************************
            TI ARM Clang Linker Unix v4.0.2                    
******************************************************************************
>> Linked Thu Jul 24 15:15:18 2025

OUTPUT FILE NAME:   <2.8_lcd_adc.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001ca9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00002908  0001d6f8  R  X
  SRAM                  20200000   00008000  000013ed  00006c13  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002908   00002908    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002148   00002148    r-x .text
  00002208    00002208    000006d0   000006d0    r-- .rodata
  000028d8    000028d8    00000030   00000030    r-- .cinit
20200000    20200000    000011f0   00000000    rw-
  20200000    20200000    00001009   00000000    rw- .data
  2020100c    2020100c    000001e4   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002148     
                  000000c0    00000394     empty.o (.text.main)
                  00000454    00000260     lcd_init.o (.text.LCD_Init)
                  000006b4    000001d8     lcd.o (.text.LCD_ShowChinese12x12)
                  0000088c    000001d8     lcd.o (.text.LCD_ShowChinese16x16)
                  00000a64    000001d8     lcd.o (.text.LCD_ShowChinese24x24)
                  00000c3c    000001d8     lcd.o (.text.LCD_ShowChinese32x32)
                  00000e14    00000128     lcd.o (.text.LCD_DrawLine)
                  00000f3c    00000116     lcd.o (.text.LCD_ShowChinese)
                  00001052    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001054    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000113c    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_1_init)
                  000011dc    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00001278    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001304    00000084     lcd.o (.text.LCD_Fill)
                  00001388    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000140c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000148e    00000002     --HOLE-- [fill = 0]
                  00001490    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000150c    00000078     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001584    00000064     lcd_init.o (.text.LCD_Writ_Bus)
                  000015e8    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001644    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  0000169c    00000052     lcd_init.o (.text.LCD_Address_Set)
                  000016ee    00000002     --HOLE-- [fill = 0]
                  000016f0    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  0000173c    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00001788    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000017d2    00000002     --HOLE-- [fill = 0]
                  000017d4    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00001818    00000040                 : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00001858    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_LCD_init)
                  00001898    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000018d8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001918    0000003c                            : floatsisf.S.obj (.text.__floatsisf)
                  00001954    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001990    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000019ca    00000002     --HOLE-- [fill = 0]
                  000019cc    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  00001a04    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00001a3c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001a70    00000030     empty.o (.text.DL_DMA_setTransferSize)
                  00001aa0    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  00001ad0    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00001b00    00000030     lcd_init.o (.text.LCD_WR_REG)
                  00001b30    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  00001b5c    0000002c     lcd.o (.text.LCD_DrawPoint)
                  00001b88    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  00001bb4    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00001be0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001c08    00000028     empty.o (.text.DL_DMA_setDestAddr)
                  00001c30    00000028     empty.o (.text.DL_DMA_setSrcAddr)
                  00001c58    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  00001c80    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00001ca8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001cd0    00000026     empty.o (.text.DL_DMA_enableChannel)
                  00001cf6    00000002     --HOLE-- [fill = 0]
                  00001cf8    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  00001d1c    00000020     empty.o (.text.ADC0_IRQHandler)
                  00001d3c    00000020     empty.o (.text.ADC1_IRQHandler)
                  00001d5c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001d7c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setMFPCLKSource)
                  00001d9c    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00001dba    0000001e     lcd_init.o (.text.LCD_WR_DATA)
                  00001dd8    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  00001df4    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  00001e10    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  00001e2c    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  00001e48    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001e64    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00001e80    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001e9c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00001eb8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001ed4    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  00001eee    00000002     --HOLE-- [fill = 0]
                  00001ef0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00001f08    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00001f20    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00001f38    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001f50    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001f68    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001f80    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001f98    00000018     lcd_init.o (.text.DL_GPIO_setPins)
                  00001fb0    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00001fc8    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00001fe0    00000018     lcd_init.o (.text.DL_SPI_isBusy)
                  00001ff8    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00002010    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002028    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002040    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002058    00000018     empty.o (.text.DL_Timer_startCounter)
                  00002070    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_CLK_init)
                  00002088    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000209e    00000016     lcd_init.o (.text.DL_SPI_transmitData8)
                  000020b4    00000016     board.o (.text.delay_ms)
                  000020ca    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000020e0    00000014     lcd_init.o (.text.DL_GPIO_clearPins)
                  000020f4    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002108    00000014     lcd_init.o (.text.DL_SPI_receiveData8)
                  0000211c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00002130    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002144    00000014     lcd_init.o (.text.LCD_WR_DATA8)
                  00002158    00000012     empty.o (.text.DL_ADC12_getPendingInterrupt)
                  0000216a    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  0000217c    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000218e    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000021a0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000021b0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFPCLK)
                  000021c0    0000000c     ti_msp_dl_config.o (.text.DL_SYSCTL_getClockStatus)
                  000021cc    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000021d8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000021e2    00000002     --HOLE-- [fill = 0]
                  000021e4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000021ec    00000006     libc.a : exit.c.obj (.text:abort)
                  000021f2    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000021f6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000021fa    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000021fe    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002202    00000006     --HOLE-- [fill = 0]

.cinit     0    000028d8    00000030     
                  000028d8    0000000c     (__TI_handler_table)
                  000028e4    00000009     (.cinit..data.load) [load image, compression = lzss]
                  000028ed    00000003     --HOLE-- [fill = 0]
                  000028f0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000028f8    00000010     (__TI_cinit_table)

.rodata    0    00002208    000006d0     
                  00002208    0000028a     lcd.o (.rodata.tfont32)
                  00002492    00000198     lcd.o (.rodata.tfont16)
                  0000262a    00000172     lcd.o (.rodata.tfont24)
                  0000279c    000000bd     lcd.o (.rodata.tfont12)
                  00002859    00000019     empty.o (.rodata.str1.9517790425240694019.1)
                  00002872    00000002     ti_msp_dl_config.o (.rodata.gSPI_LCD_clockConfig)
                  00002874    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  0000288c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  000028a4    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000028b8    0000000a     ti_msp_dl_config.o (.rodata.gSPI_LCD_config)
                  000028c2    00000002     --HOLE-- [fill = 0]
                  000028c4    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  000028cc    00000008     ti_msp_dl_config.o (.rodata.gADC12_1ClockConfig)
                  000028d4    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  000028d7    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00001009     UNINITIALIZED
                  20200000    00000800     empty.o (.data.gADCSamples)
                  20200800    00000800     empty.o (.data.gADCSamples_ch1)
                  20201000    00000004     empty.o (.data.adc0_done)
                  20201004    00000004     empty.o (.data.adc1_done)
                  20201008    00000001     empty.o (.data.g_has_prev_points)

.bss       0    2020100c    000001e4     UNINITIALIZED
                  2020100c    000000bc     (.common:gTIMER_0Backup)
                  202010c8    00000080     empty.o (.bss.g_prev_points)
                  20201148    00000080     empty.o (.bss.g_prev_points_ch1)
                  202011c8    00000028     (.common:gSPI_LCDBackup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    ./
       empty.o                        1232   25        4361   
       ti_msp_dl_config.o             2292   99        228    
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         3532   316       4589   
                                                              
    ./BSP/LCD/
       lcd.o                          2638   1617      0      
       lcd_init.o                     998    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         3636   1617      0      
                                                              
    ./Board/
       board.o                        22     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         22     0         0      
                                                              
    /Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    /Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    /Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       mulsf3.S.obj                   140    0         0      
       divsf3.S.obj                   130    0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         518    0         0      
                                                              
    /Users/<USER>/Downloads/mspm0-sdk-main/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260    0         0      
       dl_spi.o                       86     0         0      
       dl_dma.o                       76     0         0      
       dl_adc12.o                     64     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         496    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      45        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   8500   1978      5101   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000028f8 records: 2, size/record: 8, table size: 16
	.data: load addr=000028e4, load size=00000009 bytes, run addr=20200000, run size=00001009 bytes, compression=lzss
	.bss: load addr=000028f0, load size=00000008 bytes, run addr=2020100c, run size=000001e4 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000028d8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00001d1d  ADC0_IRQHandler               
00001d3d  ADC1_IRQHandler               
000021f3  AES_IRQHandler                
000021f6  C$$EXIT                       
000021f3  CANFD0_IRQHandler             
000021f3  DAC0_IRQHandler               
00001819  DL_ADC12_setClockConfig       
000021d9  DL_Common_delayCycles         
0000173d  DL_DMA_initChannel            
000017d5  DL_SPI_init                   
0000216b  DL_SPI_setClockConfig         
00001055  DL_Timer_initTimerMode        
00001eb9  DL_Timer_setClockConfig       
000021f3  DMA_IRQHandler                
000021f3  Default_Handler               
000021f3  GROUP0_IRQHandler             
000021f3  GROUP1_IRQHandler             
000021f7  HOSTexit                      
000021f3  HardFault_Handler             
000021f3  I2C0_IRQHandler               
000021f3  I2C1_IRQHandler               
0000169d  LCD_Address_Set               
00000e15  LCD_DrawLine                  
00001b5d  LCD_DrawPoint                 
00001305  LCD_Fill                      
00000455  LCD_Init                      
00000f3d  LCD_ShowChinese               
000006b5  LCD_ShowChinese12x12          
0000088d  LCD_ShowChinese16x16          
00000a65  LCD_ShowChinese24x24          
00000c3d  LCD_ShowChinese32x32          
00001dbb  LCD_WR_DATA                   
00002145  LCD_WR_DATA8                  
00001b01  LCD_WR_REG                    
00001585  LCD_Writ_Bus                  
000021f3  NMI_Handler                   
000021f3  PendSV_Handler                
000021f3  RTC_IRQHandler                
000021fb  Reset_Handler                 
000021f3  SPI0_IRQHandler               
000021f3  SPI1_IRQHandler               
000021f3  SVC_Handler                   
000011dd  SYSCFG_DL_ADC12_0_init        
0000113d  SYSCFG_DL_ADC12_1_init        
00001c81  SYSCFG_DL_DMA_CH0_init        
00001b89  SYSCFG_DL_DMA_CH1_init        
000021cd  SYSCFG_DL_DMA_init            
000015e9  SYSCFG_DL_GPIO_init           
00001859  SYSCFG_DL_SPI_LCD_init        
00002071  SYSCFG_DL_SYSCTL_CLK_init     
00001a3d  SYSCFG_DL_SYSCTL_init         
00001645  SYSCFG_DL_TIMER_0_init        
00001899  SYSCFG_DL_init                
0000150d  SYSCFG_DL_initPower           
000021f3  SysTick_Handler               
000021f3  TIMA0_IRQHandler              
000021f3  TIMA1_IRQHandler              
000021f3  TIMG0_IRQHandler              
000021f3  TIMG12_IRQHandler             
000021f3  TIMG6_IRQHandler              
000021f3  TIMG7_IRQHandler              
000021f3  TIMG8_IRQHandler              
0000217d  TI_memcpy_small               
000021f3  UART0_IRQHandler              
000021f3  UART1_IRQHandler              
000021f3  UART2_IRQHandler              
000021f3  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000028f8  __TI_CINIT_Base               
00002908  __TI_CINIT_Limit              
00002908  __TI_CINIT_Warm               
000028d8  __TI_Handler_Table_Base       
000028e4  __TI_Handler_Table_Limit      
00001955  __TI_auto_init_nobinit_nopinit
00001491  __TI_decompress_lzss          
0000218f  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
000020cb  __TI_zero_init_nomemset       
00001a05  __aeabi_f2iz                  
0000140d  __aeabi_fdiv                  
00001279  __aeabi_fmul                  
00001919  __aeabi_i2f                   
00001053  __aeabi_idiv0                 
000021e5  __aeabi_memcpy                
000021e5  __aeabi_memcpy4               
000021e5  __aeabi_memcpy8               
000018d9  __aeabi_uidiv                 
000018d9  __aeabi_uidivmod              
ffffffff  __binit__                     
0000140d  __divsf3                      
00001a05  __fixsfsi                     
00001919  __floatsisf                   
UNDEFED   __mpu_init                    
00001991  __muldsi3                     
00001279  __mulsf3                      
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00001ca9  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000021ff  _system_pre_init              
000021ed  abort                         
20201000  adc0_done                     
20201004  adc1_done                     
ffffffff  binit                         
000020b5  delay_ms                      
20200000  gADCSamples                   
20200800  gADCSamples_ch1               
202011c8  gSPI_LCDBackup                
2020100c  gTIMER_0Backup                
00000000  interruptVectors              
000000c1  main                          
0000279c  tfont12                       
00002492  tfont16                       
0000262a  tfont24                       
00002208  tfont32                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  main                          
00000200  __STACK_SIZE                  
00000455  LCD_Init                      
000006b5  LCD_ShowChinese12x12          
0000088d  LCD_ShowChinese16x16          
00000a65  LCD_ShowChinese24x24          
00000c3d  LCD_ShowChinese32x32          
00000e15  LCD_DrawLine                  
00000f3d  LCD_ShowChinese               
00001053  __aeabi_idiv0                 
00001055  DL_Timer_initTimerMode        
0000113d  SYSCFG_DL_ADC12_1_init        
000011dd  SYSCFG_DL_ADC12_0_init        
00001279  __aeabi_fmul                  
00001279  __mulsf3                      
00001305  LCD_Fill                      
0000140d  __aeabi_fdiv                  
0000140d  __divsf3                      
00001491  __TI_decompress_lzss          
0000150d  SYSCFG_DL_initPower           
00001585  LCD_Writ_Bus                  
000015e9  SYSCFG_DL_GPIO_init           
00001645  SYSCFG_DL_TIMER_0_init        
0000169d  LCD_Address_Set               
0000173d  DL_DMA_initChannel            
000017d5  DL_SPI_init                   
00001819  DL_ADC12_setClockConfig       
00001859  SYSCFG_DL_SPI_LCD_init        
00001899  SYSCFG_DL_init                
000018d9  __aeabi_uidiv                 
000018d9  __aeabi_uidivmod              
00001919  __aeabi_i2f                   
00001919  __floatsisf                   
00001955  __TI_auto_init_nobinit_nopinit
00001991  __muldsi3                     
00001a05  __aeabi_f2iz                  
00001a05  __fixsfsi                     
00001a3d  SYSCFG_DL_SYSCTL_init         
00001b01  LCD_WR_REG                    
00001b5d  LCD_DrawPoint                 
00001b89  SYSCFG_DL_DMA_CH1_init        
00001c81  SYSCFG_DL_DMA_CH0_init        
00001ca9  _c_int00_noargs               
00001d1d  ADC0_IRQHandler               
00001d3d  ADC1_IRQHandler               
00001dbb  LCD_WR_DATA                   
00001eb9  DL_Timer_setClockConfig       
00002071  SYSCFG_DL_SYSCTL_CLK_init     
000020b5  delay_ms                      
000020cb  __TI_zero_init_nomemset       
00002145  LCD_WR_DATA8                  
0000216b  DL_SPI_setClockConfig         
0000217d  TI_memcpy_small               
0000218f  __TI_decompress_none          
000021cd  SYSCFG_DL_DMA_init            
000021d9  DL_Common_delayCycles         
000021e5  __aeabi_memcpy                
000021e5  __aeabi_memcpy4               
000021e5  __aeabi_memcpy8               
000021ed  abort                         
000021f3  AES_IRQHandler                
000021f3  CANFD0_IRQHandler             
000021f3  DAC0_IRQHandler               
000021f3  DMA_IRQHandler                
000021f3  Default_Handler               
000021f3  GROUP0_IRQHandler             
000021f3  GROUP1_IRQHandler             
000021f3  HardFault_Handler             
000021f3  I2C0_IRQHandler               
000021f3  I2C1_IRQHandler               
000021f3  NMI_Handler                   
000021f3  PendSV_Handler                
000021f3  RTC_IRQHandler                
000021f3  SPI0_IRQHandler               
000021f3  SPI1_IRQHandler               
000021f3  SVC_Handler                   
000021f3  SysTick_Handler               
000021f3  TIMA0_IRQHandler              
000021f3  TIMA1_IRQHandler              
000021f3  TIMG0_IRQHandler              
000021f3  TIMG12_IRQHandler             
000021f3  TIMG6_IRQHandler              
000021f3  TIMG7_IRQHandler              
000021f3  TIMG8_IRQHandler              
000021f3  UART0_IRQHandler              
000021f3  UART1_IRQHandler              
000021f3  UART2_IRQHandler              
000021f3  UART3_IRQHandler              
000021f6  C$$EXIT                       
000021f7  HOSTexit                      
000021fb  Reset_Handler                 
000021ff  _system_pre_init              
00002208  tfont32                       
00002492  tfont16                       
0000262a  tfont24                       
0000279c  tfont12                       
000028d8  __TI_Handler_Table_Base       
000028e4  __TI_Handler_Table_Limit      
000028f8  __TI_CINIT_Base               
00002908  __TI_CINIT_Limit              
00002908  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gADCSamples                   
20200800  gADCSamples_ch1               
20201000  adc0_done                     
20201004  adc1_done                     
2020100c  gTIMER_0Backup                
202011c8  gSPI_LCDBackup                
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[131 symbols]
