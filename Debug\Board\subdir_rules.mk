################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
Board/%.o: ../Board/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"G:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/2.8_lcd_adc_NOLVGL_DDS" -I"C:/Users/<USER>/workspace_ccstheia/2.8_lcd_adc_NOLVGL_DDS/Board" -I"C:/Users/<USER>/workspace_ccstheia/2.8_lcd_adc_NOLVGL_DDS/BSP" -I"C:/Users/<USER>/workspace_ccstheia/2.8_lcd_adc_NOLVGL_DDS/BSP/LCD" -I"C:/Users/<USER>/workspace_ccstheia/2.8_lcd_adc_NOLVGL_DDS/Debug" -I"G:/ti/SDK/mspm0-sdk-main/source/third_party/CMSIS/Core/Include" -I"G:/ti/SDK/mspm0-sdk-main/source" -gdwarf-3 -MMD -MP -MF"Board/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


