******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Sun Jul 27 17:42:38 2025

OUTPUT FILE NAME:   <2.8_lcd_adc_NOLVGL_DDS.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00006a69


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00007108  00018ef8  R  X
  SRAM                  20200000   00008000  000033f5  00004c0b  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00007108   00007108    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003b20   00003b20    r-- .rodata
  00003be0    00003be0    000034f0   000034f0    r-x .text
  000070d0    000070d0    00000038   00000038    r-- .cinit
20200000    20200000    000031f5   00000000    rw-
  20200000    20200000    000021e4   00000000    rw- .bss
  202021e4    202021e4    00001011   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    00003be0    000034f0     
                  00003be0    00000634     empty.o (.text.main)
                  00004214    00000260     lcd_init.o (.text.LCD_Init)
                  00004474    00000214     lcd.o (.text.LCD_ShowChar)
                  00004688    000001d8     lcd.o (.text.LCD_ShowChinese12x12)
                  00004860    000001d8     lcd.o (.text.LCD_ShowChinese16x16)
                  00004a38    000001d8     lcd.o (.text.LCD_ShowChinese24x24)
                  00004c10    000001d8     lcd.o (.text.LCD_ShowChinese32x32)
                  00004de8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00004f7a    00000128     lcd.o (.text.LCD_DrawLine)
                  000050a2    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000050a4    0000011c     lcd.o (.text.LCD_ShowFloatNum1)
                  000051c0    00000116     lcd.o (.text.LCD_ShowChinese)
                  000052d6    00000002     --HOLE-- [fill = 0]
                  000052d8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000053e4    00000108     ad9850.o (.text.dds_set)
                  000054ec    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000055d4    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000056b8    000000d8                            : addsf3.S.obj (.text)
                  00005790    000000d2     arm_cortexM0l_math.a : arm_rms_f32.o (.text.arm_rms_f32)
                  00005862    000000b4                          : arm_offset_f32.o (.text.arm_offset_f32)
                  00005916    00000002     --HOLE-- [fill = 0]
                  00005918    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000059c8    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_1_init)
                  00005a68    000000a0     libc.a : e_sqrtf.c.obj (.text.sqrtf)
                  00005b08    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00005ba4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005c30    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005cbc    00000088     arm_cortexM0l_math.a : arm_mean_f32.o (.text.arm_mean_f32)
                  00005d44    00000084     lcd.o (.text.LCD_Fill)
                  00005dc8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005e4c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005ece    00000002     --HOLE-- [fill = 0]
                  00005ed0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005f4c    0000007a     lcd.o (.text.LCD_ShowString)
                  00005fc6    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005fd0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00006044    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000060a8    00000064     lcd_init.o (.text.LCD_Writ_Bus)
                  0000610c    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000616e    00000002     --HOLE-- [fill = 0]
                  00006170    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000061c8    00000052     lcd_init.o (.text.LCD_Address_Set)
                  0000621a    00000002     --HOLE-- [fill = 0]
                  0000621c    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00006268    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000062b4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006300    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000634a    00000002     --HOLE-- [fill = 0]
                  0000634c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006394    00000044                 : dl_spi.o (.text.DL_SPI_init)
                  000063d8    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000641c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000645e    00000002     --HOLE-- [fill = 0]
                  00006460    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000064a0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_Console_init)
                  000064e0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFTspi_init)
                  00006520    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006560    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000065a0    0000003c                            : floatsisf.S.obj (.text.__floatsisf)
                  000065dc    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006618    0000003c     ad9850.o (.text.dds_databitwrite)
                  00006654    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006690    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000066ca    00000002     --HOLE-- [fill = 0]
                  000066cc    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006706    00000002     --HOLE-- [fill = 0]
                  00006708    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  00006740    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006778    00000034     ad9850.o (.text.dds_reset)
                  000067ac    00000030     empty.o (.text.DL_DMA_setTransferSize)
                  000067dc    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  0000680c    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  0000683c    00000030     lcd_init.o (.text.LCD_WR_REG)
                  0000686c    00000030     lcd.o (.text.mypow)
                  0000689c    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  000068c8    0000002c     lcd.o (.text.LCD_DrawPoint)
                  000068f4    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  00006920    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  0000694c    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006976    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000699e    00000002     --HOLE-- [fill = 0]
                  000069a0    00000028     empty.o (.text.DL_DMA_setDestAddr)
                  000069c8    00000028     empty.o (.text.DL_DMA_setSrcAddr)
                  000069f0    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  00006a18    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00006a40    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00006a68    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00006a90    00000026     empty.o (.text.DL_DMA_enableChannel)
                  00006ab6    00000002     --HOLE-- [fill = 0]
                  00006ab8    00000024     empty.o (.text.DL_ADC12_getMemResultAddress)
                  00006adc    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  00006b00    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00006b24    00000020     empty.o (.text.ADC0_IRQHandler)
                  00006b44    00000020     empty.o (.text.ADC1_IRQHandler)
                  00006b64    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00006b84    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00006ba2    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00006bc0    0000001e     lcd_init.o (.text.LCD_WR_DATA)
                  00006bde    00000002     --HOLE-- [fill = 0]
                  00006be0    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  00006bfc    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  00006c18    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  00006c34    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  00006c50    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00006c6c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00006c88    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00006ca4    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  00006cbe    00000002     --HOLE-- [fill = 0]
                  00006cc0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00006cd8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00006cf0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00006d08    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00006d20    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00006d38    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00006d50    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00006d68    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00006d80    00000018     ad9850.o (.text.DL_GPIO_setPins)
                  00006d98    00000018     lcd_init.o (.text.DL_GPIO_setPins)
                  00006db0    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00006dc8    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00006de0    00000018     lcd_init.o (.text.DL_SPI_isBusy)
                  00006df8    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00006e10    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00006e28    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00006e40    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00006e58    00000018     empty.o (.text.DL_Timer_startCounter)
                  00006e70    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00006e88    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00006ea0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_CLK_init)
                  00006eb8    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00006ece    00000016     lcd_init.o (.text.DL_SPI_transmitData8)
                  00006ee4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00006efa    00000016     board.o (.text.delay_ms)
                  00006f10    00000014     ad9850.o (.text.DL_GPIO_clearPins)
                  00006f24    00000014     lcd_init.o (.text.DL_GPIO_clearPins)
                  00006f38    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00006f4c    00000014     lcd_init.o (.text.DL_SPI_receiveData8)
                  00006f60    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00006f74    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00006f88    00000014     lcd_init.o (.text.LCD_WR_DATA8)
                  00006f9c    00000014     ad9850.o (.text.dds_clkclr)
                  00006fb0    00000014     ad9850.o (.text.dds_clkset)
                  00006fc4    00000014     ad9850.o (.text.dds_datclr)
                  00006fd8    00000014     ad9850.o (.text.dds_datset)
                  00006fec    00000012     empty.o (.text.DL_ADC12_getPendingInterrupt)
                  00006ffe    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00007010    00000012                 : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007022    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007034    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007046    00000002     --HOLE-- [fill = 0]
                  00007048    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007058    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00007068    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00007076    00000002     --HOLE-- [fill = 0]
                  00007078    0000000c     ti_msp_dl_config.o (.text.DL_SYSCTL_getClockStatus)
                  00007084    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007090    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000709c    0000000a     ad9850.o (.text.dds_clkdelay)
                  000070a6    00000002     --HOLE-- [fill = 0]
                  000070a8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000070b0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000070b8    00000006     libc.a : exit.c.obj (.text:abort)
                  000070be    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000070c2    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000070c6    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000070ca    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000070ce    00000002     --HOLE-- [fill = 0]

.cinit     0    000070d0    00000038     
                  000070d0    0000000d     (.cinit..data.load) [load image, compression = lzss]
                  000070dd    00000003     --HOLE-- [fill = 0]
                  000070e0    0000000c     (__TI_handler_table)
                  000070ec    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000070f4    00000010     (__TI_cinit_table)
                  00007104    00000004     --HOLE-- [fill = 0]

.rodata    0    000000c0    00003b20     
                  000000c0    000017c0     lcd.o (.rodata.ascii_3216)
                  00001880    000011d0     lcd.o (.rodata.ascii_2412)
                  00002a50    000005f0     lcd.o (.rodata.ascii_1608)
                  00003040    00000474     lcd.o (.rodata.ascii_1206)
                  000034b4    0000028a     lcd.o (.rodata.tfont32)
                  0000373e    00000198     lcd.o (.rodata.tfont16)
                  000038d6    00000172     lcd.o (.rodata.tfont24)
                  00003a48    000000bd     lcd.o (.rodata.tfont12)
                  00003b05    00000019     empty.o (.rodata.str1.16704889451495720520.1)
                  00003b1e    00000002     ti_msp_dl_config.o (.rodata.gConsoleClockConfig)
                  00003b20    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00003b38    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  00003b50    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00003b64    0000000f     empty.o (.rodata.str1.11898133897667081452.1)
                  00003b73    0000000d     empty.o (.rodata.str1.17669528882079347314.1)
                  00003b80    0000000d     empty.o (.rodata.str1.7401042497206923953.1)
                  00003b8d    00000001     --HOLE-- [fill = 0]
                  00003b8e    0000000a     ti_msp_dl_config.o (.rodata.gConsoleConfig)
                  00003b98    0000000a     ti_msp_dl_config.o (.rodata.gTFTspi_config)
                  00003ba2    0000000a     empty.o (.rodata.str1.14685083708502177989.1)
                  00003bac    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00003bb4    00000008     ti_msp_dl_config.o (.rodata.gADC12_1ClockConfig)
                  00003bbc    00000008     empty.o (.rodata.str1.254342170260855183.1)
                  00003bc4    00000008     empty.o (.rodata.str1.9517790425240694019.1)
                  00003bcc    00000004     empty.o (.rodata.str1.2196762768037919588.1)
                  00003bd0    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00003bd3    00000002     ti_msp_dl_config.o (.rodata.gTFTspi_clockConfig)
                  00003bd5    00000002     empty.o (.rodata.str1.150872071346279890.1)
                  00003bd7    00000002     empty.o (.rodata.str1.15706828512682300538.1)
                  00003bd9    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000021e4     UNINITIALIZED
                  20200000    00001000     empty.o (.bss.main.float_samples_current)
                  20201000    00001000     empty.o (.bss.main.float_samples_voltage)
                  20202000    000000bc     (.common:gTIMER_0Backup)
                  202020bc    00000080     empty.o (.bss.g_prev_points)
                  2020213c    00000080     empty.o (.bss.g_prev_points_ch1)
                  202021bc    00000028     (.common:gTFTspiBackup)

.data      0    202021e4    00001011     UNINITIALIZED
                  202021e4    00000800     empty.o (.data.gADCSamples)
                  202029e4    00000800     empty.o (.data.gADCSamples_ch1)
                  202031e4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202031e8    00000004     empty.o (.data.adc0_done)
                  202031ec    00000004     empty.o (.data.adc1_done)
                  202031f0    00000004     empty.o (.data.g_current_freq_hz)
                  202031f4    00000001     empty.o (.data.g_has_prev_points)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       empty.o                        1940    100       12557  
       ti_msp_dl_config.o             2550    111       228    
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4498    403       12785  
                                                               
    .\BSP\AD9850\
       ad9850.o                       510     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         510     0         0      
                                                               
    .\BSP\LCD\
       lcd.o                          3624    14917     0      
       lcd_init.o                     998     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4622    14917     0      
                                                               
    .\Board\
       board.o                        22      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         22      0         0      
                                                               
    G:/ti/SDK/mspm0-sdk-main/source/third_party/CMSIS/DSP/lib/ticlang/m0p/arm_cortexM0l_math.a
       arm_rms_f32.o                  210     0         0      
       arm_offset_f32.o               180     0         0      
       arm_mean_f32.o                 136     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         526     0         0      
                                                               
    G:/ti/SDK/mspm0-sdk-main/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   100     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         686     0         0      
                                                               
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_sqrtf.c.obj                  160     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         468     0         4      
                                                               
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_fcmp.S.obj               98      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       aeabi_memset.S.obj             12      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2182    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       49        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   13518   15369     13301  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000070f4 records: 2, size/record: 8, table size: 16
	.data: load addr=000070d0, load size=0000000d bytes, run addr=202021e4, run size=00001011 bytes, compression=lzss
	.bss: load addr=000070ec, load size=00000008 bytes, run addr=20200000, run size=000021e4 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000070e0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                              
-------   ----                              
00006b25  ADC0_IRQHandler                   
00006b45  ADC1_IRQHandler                   
000070bf  AES_IRQHandler                    
000070c2  C$$EXIT                           
000070bf  CANFD0_IRQHandler                 
000070bf  DAC0_IRQHandler                   
00006461  DL_ADC12_setClockConfig           
00005fc7  DL_Common_delayCycles             
00006269  DL_DMA_initChannel                
00006395  DL_SPI_init                       
00006fff  DL_SPI_setClockConfig             
00006045  DL_SYSCTL_setHFCLKSourceHFXTParams
000054ed  DL_Timer_initTimerMode            
00006c89  DL_Timer_setClockConfig           
0000634d  DL_UART_init                      
00007011  DL_UART_setClockConfig            
000070bf  DMA_IRQHandler                    
000070bf  Default_Handler                   
000070bf  GROUP0_IRQHandler                 
000070bf  GROUP1_IRQHandler                 
000070c3  HOSTexit                          
000070bf  HardFault_Handler                 
000070bf  I2C0_IRQHandler                   
000070bf  I2C1_IRQHandler                   
000061c9  LCD_Address_Set                   
00004f7b  LCD_DrawLine                      
000068c9  LCD_DrawPoint                     
00005d45  LCD_Fill                          
00004215  LCD_Init                          
00004475  LCD_ShowChar                      
000051c1  LCD_ShowChinese                   
00004689  LCD_ShowChinese12x12              
00004861  LCD_ShowChinese16x16              
00004a39  LCD_ShowChinese24x24              
00004c11  LCD_ShowChinese32x32              
000050a5  LCD_ShowFloatNum1                 
00005f4d  LCD_ShowString                    
00006bc1  LCD_WR_DATA                       
00006f89  LCD_WR_DATA8                      
0000683d  LCD_WR_REG                        
000060a9  LCD_Writ_Bus                      
000070bf  NMI_Handler                       
000070bf  PendSV_Handler                    
000070bf  RTC_IRQHandler                    
000070c7  Reset_Handler                     
000070bf  SPI0_IRQHandler                   
000070bf  SPI1_IRQHandler                   
000070bf  SVC_Handler                       
00005b09  SYSCFG_DL_ADC12_0_init            
000059c9  SYSCFG_DL_ADC12_1_init            
000064a1  SYSCFG_DL_Console_init            
00006a19  SYSCFG_DL_DMA_CH0_init            
000068f5  SYSCFG_DL_DMA_CH1_init            
00007085  SYSCFG_DL_DMA_init                
00005919  SYSCFG_DL_GPIO_init               
00006ea1  SYSCFG_DL_SYSCTL_CLK_init         
0000694d  SYSCFG_DL_SYSCTL_init             
000064e1  SYSCFG_DL_TFTspi_init             
00006171  SYSCFG_DL_TIMER_0_init            
000063d9  SYSCFG_DL_init                    
00005ba5  SYSCFG_DL_initPower               
000070bf  SysTick_Handler                   
000070bf  TIMA0_IRQHandler                  
000070bf  TIMA1_IRQHandler                  
000070bf  TIMG0_IRQHandler                  
000070bf  TIMG12_IRQHandler                 
000070bf  TIMG6_IRQHandler                  
000070bf  TIMG7_IRQHandler                  
000070bf  TIMG8_IRQHandler                  
00007023  TI_memcpy_small                   
00007069  TI_memset_small                   
000070bf  UART0_IRQHandler                  
000070bf  UART1_IRQHandler                  
000070bf  UART2_IRQHandler                  
000070bf  UART3_IRQHandler                  
20208000  __STACK_END                       
00000200  __STACK_SIZE                      
00000000  __TI_ATRegion0_region_sz          
00000000  __TI_ATRegion0_src_addr           
00000000  __TI_ATRegion0_trg_addr           
00000000  __TI_ATRegion1_region_sz          
00000000  __TI_ATRegion1_src_addr           
00000000  __TI_ATRegion1_trg_addr           
00000000  __TI_ATRegion2_region_sz          
00000000  __TI_ATRegion2_src_addr           
00000000  __TI_ATRegion2_trg_addr           
000070f4  __TI_CINIT_Base                   
00007104  __TI_CINIT_Limit                  
00007104  __TI_CINIT_Warm                   
000070e0  __TI_Handler_Table_Base           
000070ec  __TI_Handler_Table_Limit          
00006655  __TI_auto_init_nobinit_nopinit    
00005ed1  __TI_decompress_lzss              
00007035  __TI_decompress_none              
ffffffff  __TI_pprof_out_hndl               
ffffffff  __TI_prof_data_size               
ffffffff  __TI_prof_data_start              
00000000  __TI_static_base__                
00007059  __TI_zero_init                    
00004df3  __adddf3                          
000056c3  __addsf3                          
00005fd1  __aeabi_d2f                       
0000641d  __aeabi_d2uiz                     
00004df3  __aeabi_dadd                      
000052d9  __aeabi_ddiv                      
000055d5  __aeabi_dmul                      
00004de9  __aeabi_dsub                      
202031e4  __aeabi_errno                     
000070a9  __aeabi_errno_addr                
00006561  __aeabi_f2d                       
00006741  __aeabi_f2iz                      
000056c3  __aeabi_fadd                      
0000610d  __aeabi_fcmpeq                    
00006149  __aeabi_fcmpge                    
0000615d  __aeabi_fcmpgt                    
00006135  __aeabi_fcmple                    
00006121  __aeabi_fcmplt                    
00005e4d  __aeabi_fdiv                      
00005c31  __aeabi_fmul                      
000056b9  __aeabi_fsub                      
000065a1  __aeabi_i2f                       
000050a3  __aeabi_idiv0                     
00007091  __aeabi_memclr                    
00007091  __aeabi_memclr4                   
00007091  __aeabi_memclr8                   
000070b1  __aeabi_memcpy                    
000070b1  __aeabi_memcpy4                   
000070b1  __aeabi_memcpy8                   
00006b01  __aeabi_ui2d                      
00006a41  __aeabi_ui2f                      
00006521  __aeabi_uidiv                     
00006521  __aeabi_uidivmod                  
ffffffff  __binit__                         
00006691  __cmpsf2                          
000052d9  __divdf3                          
00005e4d  __divsf3                          
00006691  __eqsf2                           
00006561  __extendsfdf2                     
00006741  __fixsfsi                         
0000641d  __fixunsdfsi                      
000065a1  __floatsisf                       
00006b01  __floatunsidf                     
00006a41  __floatunsisf                     
000065dd  __gesf2                           
000065dd  __gtsf2                           
00006691  __lesf2                           
00006691  __ltsf2                           
UNDEFED   __mpu_init                        
000055d5  __muldf3                          
000066cd  __muldsi3                         
00005c31  __mulsf3                          
00006691  __nesf2                           
20207e00  __stack                           
20200000  __start___llvm_prf_bits           
20200000  __start___llvm_prf_cnts           
20200000  __stop___llvm_prf_bits            
20200000  __stop___llvm_prf_cnts            
00004de9  __subdf3                          
000056b9  __subsf3                          
00005fd1  __truncdfsf2                      
00006a69  _c_int00_noargs                   
UNDEFED   _system_post_cinit                
000070cb  _system_pre_init                  
000070b9  abort                             
202031e8  adc0_done                         
202031ec  adc1_done                         
00005cbd  arm_mean_f32                      
00005863  arm_offset_f32                    
00005791  arm_rms_f32                       
00003040  ascii_1206                        
00002a50  ascii_1608                        
00001880  ascii_2412                        
000000c0  ascii_3216                        
ffffffff  binit                             
00006779  dds_reset                         
000053e5  dds_set                           
00006efb  delay_ms                          
202021e4  gADCSamples                       
202029e4  gADCSamples_ch1                   
202021bc  gTFTspiBackup                     
20202000  gTIMER_0Backup                    
00000000  interruptVectors                  
00003be1  main                              
0000686d  mypow                             
00005a69  sqrtf                             
00003a48  tfont12                           
0000373e  tfont16                           
000038d6  tfont24                           
000034b4  tfont32                           


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                              
-------   ----                              
00000000  __TI_ATRegion0_region_sz          
00000000  __TI_ATRegion0_src_addr           
00000000  __TI_ATRegion0_trg_addr           
00000000  __TI_ATRegion1_region_sz          
00000000  __TI_ATRegion1_src_addr           
00000000  __TI_ATRegion1_trg_addr           
00000000  __TI_ATRegion2_region_sz          
00000000  __TI_ATRegion2_src_addr           
00000000  __TI_ATRegion2_trg_addr           
00000000  __TI_static_base__                
00000000  interruptVectors                  
000000c0  ascii_3216                        
00000200  __STACK_SIZE                      
00001880  ascii_2412                        
00002a50  ascii_1608                        
00003040  ascii_1206                        
000034b4  tfont32                           
0000373e  tfont16                           
000038d6  tfont24                           
00003a48  tfont12                           
00003be1  main                              
00004215  LCD_Init                          
00004475  LCD_ShowChar                      
00004689  LCD_ShowChinese12x12              
00004861  LCD_ShowChinese16x16              
00004a39  LCD_ShowChinese24x24              
00004c11  LCD_ShowChinese32x32              
00004de9  __aeabi_dsub                      
00004de9  __subdf3                          
00004df3  __adddf3                          
00004df3  __aeabi_dadd                      
00004f7b  LCD_DrawLine                      
000050a3  __aeabi_idiv0                     
000050a5  LCD_ShowFloatNum1                 
000051c1  LCD_ShowChinese                   
000052d9  __aeabi_ddiv                      
000052d9  __divdf3                          
000053e5  dds_set                           
000054ed  DL_Timer_initTimerMode            
000055d5  __aeabi_dmul                      
000055d5  __muldf3                          
000056b9  __aeabi_fsub                      
000056b9  __subsf3                          
000056c3  __addsf3                          
000056c3  __aeabi_fadd                      
00005791  arm_rms_f32                       
00005863  arm_offset_f32                    
00005919  SYSCFG_DL_GPIO_init               
000059c9  SYSCFG_DL_ADC12_1_init            
00005a69  sqrtf                             
00005b09  SYSCFG_DL_ADC12_0_init            
00005ba5  SYSCFG_DL_initPower               
00005c31  __aeabi_fmul                      
00005c31  __mulsf3                          
00005cbd  arm_mean_f32                      
00005d45  LCD_Fill                          
00005e4d  __aeabi_fdiv                      
00005e4d  __divsf3                          
00005ed1  __TI_decompress_lzss              
00005f4d  LCD_ShowString                    
00005fc7  DL_Common_delayCycles             
00005fd1  __aeabi_d2f                       
00005fd1  __truncdfsf2                      
00006045  DL_SYSCTL_setHFCLKSourceHFXTParams
000060a9  LCD_Writ_Bus                      
0000610d  __aeabi_fcmpeq                    
00006121  __aeabi_fcmplt                    
00006135  __aeabi_fcmple                    
00006149  __aeabi_fcmpge                    
0000615d  __aeabi_fcmpgt                    
00006171  SYSCFG_DL_TIMER_0_init            
000061c9  LCD_Address_Set                   
00006269  DL_DMA_initChannel                
0000634d  DL_UART_init                      
00006395  DL_SPI_init                       
000063d9  SYSCFG_DL_init                    
0000641d  __aeabi_d2uiz                     
0000641d  __fixunsdfsi                      
00006461  DL_ADC12_setClockConfig           
000064a1  SYSCFG_DL_Console_init            
000064e1  SYSCFG_DL_TFTspi_init             
00006521  __aeabi_uidiv                     
00006521  __aeabi_uidivmod                  
00006561  __aeabi_f2d                       
00006561  __extendsfdf2                     
000065a1  __aeabi_i2f                       
000065a1  __floatsisf                       
000065dd  __gesf2                           
000065dd  __gtsf2                           
00006655  __TI_auto_init_nobinit_nopinit    
00006691  __cmpsf2                          
00006691  __eqsf2                           
00006691  __lesf2                           
00006691  __ltsf2                           
00006691  __nesf2                           
000066cd  __muldsi3                         
00006741  __aeabi_f2iz                      
00006741  __fixsfsi                         
00006779  dds_reset                         
0000683d  LCD_WR_REG                        
0000686d  mypow                             
000068c9  LCD_DrawPoint                     
000068f5  SYSCFG_DL_DMA_CH1_init            
0000694d  SYSCFG_DL_SYSCTL_init             
00006a19  SYSCFG_DL_DMA_CH0_init            
00006a41  __aeabi_ui2f                      
00006a41  __floatunsisf                     
00006a69  _c_int00_noargs                   
00006b01  __aeabi_ui2d                      
00006b01  __floatunsidf                     
00006b25  ADC0_IRQHandler                   
00006b45  ADC1_IRQHandler                   
00006bc1  LCD_WR_DATA                       
00006c89  DL_Timer_setClockConfig           
00006ea1  SYSCFG_DL_SYSCTL_CLK_init         
00006efb  delay_ms                          
00006f89  LCD_WR_DATA8                      
00006fff  DL_SPI_setClockConfig             
00007011  DL_UART_setClockConfig            
00007023  TI_memcpy_small                   
00007035  __TI_decompress_none              
00007059  __TI_zero_init                    
00007069  TI_memset_small                   
00007085  SYSCFG_DL_DMA_init                
00007091  __aeabi_memclr                    
00007091  __aeabi_memclr4                   
00007091  __aeabi_memclr8                   
000070a9  __aeabi_errno_addr                
000070b1  __aeabi_memcpy                    
000070b1  __aeabi_memcpy4                   
000070b1  __aeabi_memcpy8                   
000070b9  abort                             
000070bf  AES_IRQHandler                    
000070bf  CANFD0_IRQHandler                 
000070bf  DAC0_IRQHandler                   
000070bf  DMA_IRQHandler                    
000070bf  Default_Handler                   
000070bf  GROUP0_IRQHandler                 
000070bf  GROUP1_IRQHandler                 
000070bf  HardFault_Handler                 
000070bf  I2C0_IRQHandler                   
000070bf  I2C1_IRQHandler                   
000070bf  NMI_Handler                       
000070bf  PendSV_Handler                    
000070bf  RTC_IRQHandler                    
000070bf  SPI0_IRQHandler                   
000070bf  SPI1_IRQHandler                   
000070bf  SVC_Handler                       
000070bf  SysTick_Handler                   
000070bf  TIMA0_IRQHandler                  
000070bf  TIMA1_IRQHandler                  
000070bf  TIMG0_IRQHandler                  
000070bf  TIMG12_IRQHandler                 
000070bf  TIMG6_IRQHandler                  
000070bf  TIMG7_IRQHandler                  
000070bf  TIMG8_IRQHandler                  
000070bf  UART0_IRQHandler                  
000070bf  UART1_IRQHandler                  
000070bf  UART2_IRQHandler                  
000070bf  UART3_IRQHandler                  
000070c2  C$$EXIT                           
000070c3  HOSTexit                          
000070c7  Reset_Handler                     
000070cb  _system_pre_init                  
000070e0  __TI_Handler_Table_Base           
000070ec  __TI_Handler_Table_Limit          
000070f4  __TI_CINIT_Base                   
00007104  __TI_CINIT_Limit                  
00007104  __TI_CINIT_Warm                   
20200000  __start___llvm_prf_bits           
20200000  __start___llvm_prf_cnts           
20200000  __stop___llvm_prf_bits            
20200000  __stop___llvm_prf_cnts            
20202000  gTIMER_0Backup                    
202021bc  gTFTspiBackup                     
202021e4  gADCSamples                       
202029e4  gADCSamples_ch1                   
202031e4  __aeabi_errno                     
202031e8  adc0_done                         
202031ec  adc1_done                         
20207e00  __stack                           
20208000  __STACK_END                       
ffffffff  __TI_pprof_out_hndl               
ffffffff  __TI_prof_data_size               
ffffffff  __TI_prof_data_start              
ffffffff  __binit__                         
ffffffff  binit                             
UNDEFED   __mpu_init                        
UNDEFED   _system_post_cinit                

[189 symbols]
