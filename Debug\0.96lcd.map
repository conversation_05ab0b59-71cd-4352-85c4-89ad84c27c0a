******************************************************************************
            TI ARM Clang Linker Unix v4.0.2                    
******************************************************************************
>> Linked Tue Jul 22 16:21:46 2025

OUTPUT FILE NAME:   <0.96lcd.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002049


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00002130  0001ded0  R  X
  SRAM                  20200000   00008000  00000228  00007dd8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002130   00002130    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002018   00002018    r-x .text
  000020d8    000020d8    00000040   00000040    r-- .rodata
  00002118    00002118    00000018   00000018    r-- .cinit
20200000    20200000    00000028   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002018     
                  000000c0    000014c2     lcd_init.o (.text.LCD_Init)
                  00001582    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001584    000002c0     lcd.o (.text.LCD_Fill)
                  00001844    00000234     lcd_init.o (.text.LCD_Address_Set)
                  00001a78    00000124     empty.o (.text.main)
                  00001b9c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001c78    00000070     lcd_init.o (.text.LCD_WR_DATA)
                  00001ce8    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00001d4c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001db0    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001e10    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001e60    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_LCD_init)
                  00001eac    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001ef4    00000044                 : dl_spi.o (.text.DL_SPI_init)
                  00001f38    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00001f7c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001fbc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001ff8    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_CLK_init)
                  00002020    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002048    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002070    00000016            : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002086    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00002098    00000012                 : dl_uart.o (.text.DL_UART_setClockConfig)
                  000020aa    00000002     --HOLE-- [fill = 0]
                  000020ac    00000010     board.o (.text.delay_ms)
                  000020bc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000020c6    00000006     libc.a : exit.c.obj (.text:abort)
                  000020cc    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000020d0    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000020d4    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00002118    00000018     
                  00002118    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002120    00000004     (__TI_handler_table)
                  00002124    00000008     (__TI_cinit_table)
                  0000212c    00000004     --HOLE-- [fill = 0]

.rodata    0    000020d8    00000040     
                  000020d8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00002100    0000000a     ti_msp_dl_config.o (.rodata.gSPI_LCD_config)
                  0000210a    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00002114    00000002     ti_msp_dl_config.o (.rodata.gSPI_LCD_clockConfig)
                  00002116    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_LCDBackup)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    ./
       ti_msp_dl_config.o             496    64        40     
       empty.o                        292    0         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         794    256       40     
                                                              
    ./BSP/LCD/
       lcd_init.o                     5990   0         0      
       lcd.o                          704    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         6694   0         0      
                                                              
    ./Board/
       board.o                        16     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         16     0         0      
                                                              
    /home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/libc.a
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         132    0         0      
                                                              
    /home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    /home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388    0         0      
       dl_uart.o                      90     0         0      
       dl_spi.o                       86     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         574    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      20        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   8214   276       552    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002124 records: 1, size/record: 8, table size: 8
	.bss: load addr=00002118, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002120 records: 1, size/record: 4, table size: 4
	index: 0, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00001583  ADC0_IRQHandler                      
00001583  ADC1_IRQHandler                      
00001583  AES_IRQHandler                       
000020cc  C$$EXIT                              
00001583  CANFD0_IRQHandler                    
00001583  DAC0_IRQHandler                      
000020bd  DL_Common_delayCycles                
00001ef5  DL_SPI_init                          
00002087  DL_SPI_setClockConfig                
00001b9d  DL_SYSCTL_configSYSPLL               
00001ce9  DL_SYSCTL_setHFCLKSourceHFXTParams   
00001f39  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001ead  DL_UART_init                         
00002099  DL_UART_setClockConfig               
00001583  DMA_IRQHandler                       
00001583  Default_Handler                      
00001583  GROUP0_IRQHandler                    
00001583  GROUP1_IRQHandler                    
000020cd  HOSTexit                             
00001583  HardFault_Handler                    
00001583  I2C0_IRQHandler                      
00001583  I2C1_IRQHandler                      
00001845  LCD_Address_Set                      
00001585  LCD_Fill                             
000000c1  LCD_Init                             
00001c79  LCD_WR_DATA                          
00001583  NMI_Handler                          
00001583  PendSV_Handler                       
00001583  RTC_IRQHandler                       
000020d1  Reset_Handler                        
00001583  SPI0_IRQHandler                      
00001583  SPI1_IRQHandler                      
00001583  SVC_Handler                          
00001e11  SYSCFG_DL_GPIO_init                  
00001e61  SYSCFG_DL_SPI_LCD_init               
00001ff9  SYSCFG_DL_SYSCTL_CLK_init            
00001d4d  SYSCFG_DL_SYSCTL_init                
00001db1  SYSCFG_DL_UART_0_init                
00002021  SYSCFG_DL_init                       
00001f7d  SYSCFG_DL_initPower                  
00001583  SysTick_Handler                      
00001583  TIMA0_IRQHandler                     
00001583  TIMA1_IRQHandler                     
00001583  TIMG0_IRQHandler                     
00001583  TIMG12_IRQHandler                    
00001583  TIMG6_IRQHandler                     
00001583  TIMG7_IRQHandler                     
00001583  TIMG8_IRQHandler                     
00001583  UART0_IRQHandler                     
00001583  UART1_IRQHandler                     
00001583  UART2_IRQHandler                     
00001583  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00002124  __TI_CINIT_Base                      
0000212c  __TI_CINIT_Limit                     
0000212c  __TI_CINIT_Warm                      
00002120  __TI_Handler_Table_Base              
00002124  __TI_Handler_Table_Limit             
00001fbd  __TI_auto_init_nobinit_nopinit       
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00002071  __TI_zero_init_nomemset              
ffffffff  __binit__                            
UNDEFED   __mpu_init                           
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002049  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000020d5  _system_pre_init                     
000020c7  abort                                
ffffffff  binit                                
000020ad  delay_ms                             
20200000  gSPI_LCDBackup                       
00000000  interruptVectors                     
00001a79  main                                 


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  LCD_Init                             
00000200  __STACK_SIZE                         
00001583  ADC0_IRQHandler                      
00001583  ADC1_IRQHandler                      
00001583  AES_IRQHandler                       
00001583  CANFD0_IRQHandler                    
00001583  DAC0_IRQHandler                      
00001583  DMA_IRQHandler                       
00001583  Default_Handler                      
00001583  GROUP0_IRQHandler                    
00001583  GROUP1_IRQHandler                    
00001583  HardFault_Handler                    
00001583  I2C0_IRQHandler                      
00001583  I2C1_IRQHandler                      
00001583  NMI_Handler                          
00001583  PendSV_Handler                       
00001583  RTC_IRQHandler                       
00001583  SPI0_IRQHandler                      
00001583  SPI1_IRQHandler                      
00001583  SVC_Handler                          
00001583  SysTick_Handler                      
00001583  TIMA0_IRQHandler                     
00001583  TIMA1_IRQHandler                     
00001583  TIMG0_IRQHandler                     
00001583  TIMG12_IRQHandler                    
00001583  TIMG6_IRQHandler                     
00001583  TIMG7_IRQHandler                     
00001583  TIMG8_IRQHandler                     
00001583  UART0_IRQHandler                     
00001583  UART1_IRQHandler                     
00001583  UART2_IRQHandler                     
00001583  UART3_IRQHandler                     
00001585  LCD_Fill                             
00001845  LCD_Address_Set                      
00001a79  main                                 
00001b9d  DL_SYSCTL_configSYSPLL               
00001c79  LCD_WR_DATA                          
00001ce9  DL_SYSCTL_setHFCLKSourceHFXTParams   
00001d4d  SYSCFG_DL_SYSCTL_init                
00001db1  SYSCFG_DL_UART_0_init                
00001e11  SYSCFG_DL_GPIO_init                  
00001e61  SYSCFG_DL_SPI_LCD_init               
00001ead  DL_UART_init                         
00001ef5  DL_SPI_init                          
00001f39  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001f7d  SYSCFG_DL_initPower                  
00001fbd  __TI_auto_init_nobinit_nopinit       
00001ff9  SYSCFG_DL_SYSCTL_CLK_init            
00002021  SYSCFG_DL_init                       
00002049  _c_int00_noargs                      
00002071  __TI_zero_init_nomemset              
00002087  DL_SPI_setClockConfig                
00002099  DL_UART_setClockConfig               
000020ad  delay_ms                             
000020bd  DL_Common_delayCycles                
000020c7  abort                                
000020cc  C$$EXIT                              
000020cd  HOSTexit                             
000020d1  Reset_Handler                        
000020d5  _system_pre_init                     
00002120  __TI_Handler_Table_Base              
00002124  __TI_CINIT_Base                      
00002124  __TI_Handler_Table_Limit             
0000212c  __TI_CINIT_Limit                     
0000212c  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  gSPI_LCDBackup                       
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[90 symbols]
