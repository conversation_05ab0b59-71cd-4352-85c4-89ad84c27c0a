#include "board.h"
#include "lcd.h"

// 定义原点位置常量
#define ORIGIN_TOP_LEFT     0
#define ORIGIN_TOP_RIGHT    1
#define ORIGIN_BOTTOM_LEFT  2
#define ORIGIN_BOTTOM_RIGHT 3

typedef struct {
    u16 x;
    u16 y;
} Point;
 
// 静态变量保存上一次的波形点
static Point prevWavePoints[320];  // 最大支持320点
static u16 prevWaveWidth = 0;
static u16 prevWaveColor = 0;
static u16 prevWaveBgColor = 0;


void LCD_DrawGraph(Point* points, u16 count, u16 color);
static u16 transform_x(u16 x, u16 width, u8 origin);
static u16 transform_y(u16 y, u16 height, u8 origin);
void LCD_ClearPrevWaveform(u16 bgcolor);
void LCD_ShowWaveform(u16 x0, u16 y0, u16 width, u16 height, 
                     uint16_t* data, u16 data_len, 
                     u16 color, u16 bgcolor, u8 origin);