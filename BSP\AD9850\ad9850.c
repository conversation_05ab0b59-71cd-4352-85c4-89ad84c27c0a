#include "ad9850.h"
#include "ti_msp_dl_config.h"

// Define GPIOs matching the current SysConfig settings for GPIOB
#define IOA_W_CLK_PORT       GPIOB
#define IOA_W_CLK_PIN        DL_GPIO_PIN_13
#define IOA_DDS_DATA_PORT    GPIOB
#define IOA_DDS_DATA_PIN     DL_GPIO_PIN_15
#define IOA_FQ_UD_PORT       GPIOB
#define IOA_FQ_UD_PIN        DL_GPIO_PIN_16
#define IOA_DDS_RESET_PORT   GPIOA
#define IOA_DDS_RESET_PIN    DL_GPIO_PIN_12

// DDS control inline functions from the reference code for efficiency
static inline void dds_clkset() { DL_GPIO_setPins(IOA_W_CLK_PORT, IOA_W_CLK_PIN); }
static inline void dds_datset() { DL_GPIO_setPins(IOA_DDS_DATA_PORT, IOA_DDS_DATA_PIN); }
static inline void dds_clkclr() { DL_GPIO_clearPins(IOA_W_CLK_PORT, IOA_W_CLK_PIN); }
static inline void dds_datclr() { DL_GPIO_clearPins(IOA_DDS_DATA_PORT, IOA_DDS_DATA_PIN); }

// Delay between DDS clock signals for timing
static inline void dds_clkdelay() {
    // This is approx 1us delay at 32MHz CPU Clock
    delay_cycles(32); 
}

static inline void dds_databitwrite(uint8_t bit) {
    bit = (bit != 0);
    dds_clkclr();
    if(bit) 
        dds_datset();
    else
        dds_datclr();
    dds_clkdelay();
    dds_clkset();
    dds_clkdelay();
}

void dds_set(uint32_t freq_in_hz) {
    // The reference code's math was expecting freq in 0.1Hz units.
    // This implementation expects Hz, so we calculate freq_MHz directly.
    float freq_MHZ = freq_in_hz / 1000000.0;
    
    // Phase increment calculation, using 125MHz as the DDS reference clock
    uint32_t delta_phase = (uint32_t)(freq_MHZ * 4294967296.0 / 125.0 + 0.5);

    // Pull FQ_UD low to prepare for frequency update
    DL_GPIO_clearPins(IOA_FQ_UD_PORT, IOA_FQ_UD_PIN);
    
    // Send 32-bit frequency word (LSB first)
    for(int i = 0; i < 32; i++){
        dds_databitwrite(delta_phase & 1);
        delta_phase >>= 1;
    }
    
    // Send reserved bits
    for(int i = 0; i < 2; i++){
        dds_databitwrite(0);
    }
    
    // Send power-down bit (0 = normal operation)
    for(int i = 0; i < 1; i++){
        dds_databitwrite(0);
    }
    
    // Send 5-bit phase word (unused, all zeros)
    for(int i = 0; i < 5; i++){
        dds_databitwrite(0);
    }
    
    dds_clkclr();
    
    // Pull FQ_UD high to latch the new frequency setting
    DL_GPIO_setPins(IOA_FQ_UD_PORT, IOA_FQ_UD_PIN);
    // Use a long delay matching the reference code (approx. 100us at 32MHz)
    delay_cycles(3200); 
    DL_GPIO_clearPins(IOA_FQ_UD_PORT, IOA_FQ_UD_PIN);
}

void dds_reset(void) {
    DL_GPIO_clearPins(IOA_DDS_RESET_PORT, IOA_DDS_RESET_PIN);
    DL_GPIO_setPins(IOA_DDS_RESET_PORT, IOA_DDS_RESET_PIN);
    // Use a long delay matching the reference code (approx. 1ms at 32MHz)
    delay_cycles(32000); 
    DL_GPIO_clearPins(IOA_DDS_RESET_PORT, IOA_DDS_RESET_PIN);
} 