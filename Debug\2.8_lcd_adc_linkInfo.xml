<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker Unix v4.0.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/bin/tiarmlnk -I/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib -o 2.8_lcd_adc.out -m2.8_lcd_adc.map -i/Users/<USER>/Downloads/mspm0-sdk-main/source -i/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc -i/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/syscfg -i/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=2.8_lcd_adc_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./wave.o ./BSP/LCD/lcd.o ./BSP/LCD/lcd_init.o ./Board/board.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x6881dd86</link_time>
   <link_errors>0x0</link_errors>
   <output_file>/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/2.8_lcd_adc.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1ca9</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/./</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/./</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/./</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/./</path>
         <kind>object</kind>
         <file>wave.o</file>
         <name>wave.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/./BSP/LCD/</path>
         <kind>object</kind>
         <file>lcd.o</file>
         <name>lcd.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/./BSP/LCD/</path>
         <kind>object</kind>
         <file>lcd_init.o</file>
         <name>lcd_init.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/./Board/</path>
         <kind>object</kind>
         <file>board.o</file>
         <name>board.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>/Users/<USER>/Desktop/workspace_ccstheia/2.8_lcd_adc/Debug/</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-15">
         <path>/Users/<USER>/Downloads/mspm0-sdk-main/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>/Users/<USER>/Downloads/mspm0-sdk-main/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>/Users/<USER>/Downloads/mspm0-sdk-main/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_dma.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>/Users/<USER>/Downloads/mspm0-sdk-main/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>/Users/<USER>/Downloads/mspm0-sdk-main/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-30">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-e0">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-e1">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-e2">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-e3">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-e4">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-e5">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-e6">
         <path>/Applications/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text.main</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x394</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text.LCD_Init</name>
         <load_address>0x454</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x454</run_address>
         <size>0x260</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.LCD_ShowChinese12x12</name>
         <load_address>0x6b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b4</run_address>
         <size>0x1d8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.LCD_ShowChinese16x16</name>
         <load_address>0x88c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x88c</run_address>
         <size>0x1d8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-100">
         <name>.text.LCD_ShowChinese24x24</name>
         <load_address>0xa64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa64</run_address>
         <size>0x1d8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-101">
         <name>.text.LCD_ShowChinese32x32</name>
         <load_address>0xc3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc3c</run_address>
         <size>0x1d8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.text.LCD_DrawLine</name>
         <load_address>0xe14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe14</run_address>
         <size>0x128</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.LCD_ShowChinese</name>
         <load_address>0xf3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf3c</run_address>
         <size>0x116</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x1052</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1052</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x1054</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1054</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.SYSCFG_DL_ADC12_1_init</name>
         <load_address>0x113c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x113c</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-de">
         <name>.text.SYSCFG_DL_ADC12_0_init</name>
         <load_address>0x11dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11dc</run_address>
         <size>0x9c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.text.__mulsf3</name>
         <load_address>0x1278</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1278</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-94">
         <name>.text.LCD_Fill</name>
         <load_address>0x1304</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1304</run_address>
         <size>0x84</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-150">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x1388</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1388</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text.__divsf3</name>
         <load_address>0x140c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x140c</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x1490</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1490</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x150c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x150c</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-157">
         <name>.text.LCD_Writ_Bus</name>
         <load_address>0x1584</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1584</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x15e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15e8</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x1644</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1644</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.LCD_Address_Set</name>
         <load_address>0x169c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x169c</run_address>
         <size>0x52</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_ADC12_initSingleSample</name>
         <load_address>0x16f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16f0</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-174">
         <name>.text.DL_DMA_initChannel</name>
         <load_address>0x173c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x173c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.DL_ADC12_configConversionMem</name>
         <load_address>0x1788</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1788</run_address>
         <size>0x4a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.DL_SPI_init</name>
         <load_address>0x17d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17d4</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0x1818</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1818</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.SYSCFG_DL_SPI_LCD_init</name>
         <load_address>0x1858</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1858</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-83">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x1898</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1898</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-159">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x18d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18d8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text.__floatsisf</name>
         <load_address>0x1918</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1918</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1954</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1954</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.__muldsi3</name>
         <load_address>0x1990</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1990</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-131">
         <name>.text.DL_Timer_setPublisherChanID</name>
         <load_address>0x19cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19cc</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.__fixsfsi</name>
         <load_address>0x1a04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a04</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x1a3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a3c</run_address>
         <size>0x34</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.DL_DMA_setTransferSize</name>
         <load_address>0x1a70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a70</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-173">
         <name>.text.DL_DMA_setTransferSize</name>
         <load_address>0x1aa0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1aa0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.DL_SPI_setFIFOThreshold</name>
         <load_address>0x1ad0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ad0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.LCD_WR_REG</name>
         <load_address>0x1b00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b00</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.DL_ADC12_setDMASamplesCnt</name>
         <load_address>0x1b30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b30</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.text.LCD_DrawPoint</name>
         <load_address>0x1b5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b5c</run_address>
         <size>0x2c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-155">
         <name>.text.SYSCFG_DL_DMA_CH1_init</name>
         <load_address>0x1b88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b88</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x1bb4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bb4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1be0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1be0</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.DL_DMA_setDestAddr</name>
         <load_address>0x1c08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c08</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.DL_DMA_setSrcAddr</name>
         <load_address>0x1c30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c30</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-130">
         <name>.text.DL_Timer_enableEvent</name>
         <load_address>0x1c58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c58</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-154">
         <name>.text.SYSCFG_DL_DMA_CH0_init</name>
         <load_address>0x1c80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c80</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1ca8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ca8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.DL_DMA_enableChannel</name>
         <load_address>0x1cd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cd0</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.DL_SPI_setBitRateSerialClockDivider</name>
         <load_address>0x1cf8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cf8</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.ADC0_IRQHandler</name>
         <load_address>0x1d1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d1c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.ADC1_IRQHandler</name>
         <load_address>0x1d3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d3c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x1d5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d5c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.DL_SYSCTL_setMFPCLKSource</name>
         <load_address>0x1d7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d7c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_ADC12_setPowerDownMode</name>
         <load_address>0x1d9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d9c</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.LCD_WR_DATA</name>
         <load_address>0x1dba</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dba</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.DL_ADC12_clearInterruptStatus</name>
         <load_address>0x1dd8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dd8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.DL_ADC12_enableDMA</name>
         <load_address>0x1df4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1df4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.DL_ADC12_enableDMATrigger</name>
         <load_address>0x1e10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e10</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.text.DL_ADC12_enableInterrupt</name>
         <load_address>0x1e2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e2c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1e48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e48</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-125">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0x1e64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e64</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-121">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x1e80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e80</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x1e9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e9c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x1eb8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1eb8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.DL_ADC12_setSubscriberChanID</name>
         <load_address>0x1ed4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ed4</run_address>
         <size>0x1a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.text.DL_ADC12_enablePower</name>
         <load_address>0x1ef0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ef0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-116">
         <name>.text.DL_ADC12_reset</name>
         <load_address>0x1f08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f08</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.DL_ADC12_setSampleTime0</name>
         <load_address>0x1f20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f20</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1f38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f38</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-117">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1f50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f50</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1f68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f68</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x1f80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f80</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1f98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f98</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.DL_SPI_enable</name>
         <load_address>0x1fb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fb0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-119">
         <name>.text.DL_SPI_enablePower</name>
         <load_address>0x1fc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fc8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.text.DL_SPI_isBusy</name>
         <load_address>0x1fe0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fe0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text.DL_SPI_reset</name>
         <load_address>0x1ff8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ff8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x2010</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2010</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-118">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x2028</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2028</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x2040</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2040</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x2058</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2058</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.text.SYSCFG_DL_SYSCTL_CLK_init</name>
         <load_address>0x2070</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2070</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.DL_ADC12_enableConversions</name>
         <load_address>0x2088</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2088</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.text.DL_SPI_transmitData8</name>
         <load_address>0x209e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x209e</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text.delay_ms</name>
         <load_address>0x20b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20b4</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x20ca</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20ca</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x20e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20e0</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x20f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20f4</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.text.DL_SPI_receiveData8</name>
         <load_address>0x2108</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2108</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-123">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x211c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x211c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x2130</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2130</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.LCD_WR_DATA8</name>
         <load_address>0x2144</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2144</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text.DL_ADC12_getPendingInterrupt</name>
         <load_address>0x2158</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2158</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x216a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x216a</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x217c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x217c</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x218e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x218e</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x21a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21a0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.DL_SYSCTL_enableMFPCLK</name>
         <load_address>0x21b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21b0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-156">
         <name>.text.DL_SYSCTL_getClockStatus</name>
         <load_address>0x21c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21c0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.SYSCFG_DL_DMA_init</name>
         <load_address>0x21cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21cc</run_address>
         <size>0xc</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x21d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21d8</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x21e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21e4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text:abort</name>
         <load_address>0x21ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21ec</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x21f2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21f2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.HOSTexit</name>
         <load_address>0x21f6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21f6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x21fa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21fa</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.text._system_pre_init</name>
         <load_address>0x21fe</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21fe</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>__TI_handler_table</name>
         <load_address>0x28d8</load_address>
         <readonly>true</readonly>
         <run_address>0x28d8</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1ca">
         <name>.cinit..data.load</name>
         <load_address>0x28e4</load_address>
         <readonly>true</readonly>
         <run_address>0x28e4</run_address>
         <size>0x9</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1cb">
         <name>.cinit..bss.load</name>
         <load_address>0x28f0</load_address>
         <readonly>true</readonly>
         <run_address>0x28f0</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1c9">
         <name>__TI_cinit_table</name>
         <load_address>0x28f8</load_address>
         <readonly>true</readonly>
         <run_address>0x28f8</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-160">
         <name>.rodata.tfont32</name>
         <load_address>0x2208</load_address>
         <readonly>true</readonly>
         <run_address>0x2208</run_address>
         <size>0x28a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.rodata.tfont16</name>
         <load_address>0x2492</load_address>
         <readonly>true</readonly>
         <run_address>0x2492</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.rodata.tfont24</name>
         <load_address>0x262a</load_address>
         <readonly>true</readonly>
         <run_address>0x262a</run_address>
         <size>0x172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.rodata.tfont12</name>
         <load_address>0x279c</load_address>
         <readonly>true</readonly>
         <run_address>0x279c</run_address>
         <size>0xbd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.rodata.str1.9517790425240694019.1</name>
         <load_address>0x2859</load_address>
         <readonly>true</readonly>
         <run_address>0x2859</run_address>
         <size>0x19</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.rodata.gSPI_LCD_clockConfig</name>
         <load_address>0x2872</load_address>
         <readonly>true</readonly>
         <run_address>0x2872</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-178">
         <name>.rodata.gDMA_CH0Config</name>
         <load_address>0x2874</load_address>
         <readonly>true</readonly>
         <run_address>0x2874</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-179">
         <name>.rodata.gDMA_CH1Config</name>
         <load_address>0x288c</load_address>
         <readonly>true</readonly>
         <run_address>0x288c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-133">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0x28a4</load_address>
         <readonly>true</readonly>
         <run_address>0x28a4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.rodata.gSPI_LCD_config</name>
         <load_address>0x28b8</load_address>
         <readonly>true</readonly>
         <run_address>0x28b8</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-152">
         <name>.rodata.gADC12_0ClockConfig</name>
         <load_address>0x28c4</load_address>
         <readonly>true</readonly>
         <run_address>0x28c4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-153">
         <name>.rodata.gADC12_1ClockConfig</name>
         <load_address>0x28cc</load_address>
         <readonly>true</readonly>
         <run_address>0x28cc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-132">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0x28d4</load_address>
         <readonly>true</readonly>
         <run_address>0x28d4</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-192">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b3">
         <name>.data.gADCSamples</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x800</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.data.adc0_done</name>
         <load_address>0x20201000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201000</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.data.gADCSamples_ch1</name>
         <load_address>0x20200800</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200800</run_address>
         <size>0x800</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-60">
         <name>.data.adc1_done</name>
         <load_address>0x20201004</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201004</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.data.g_has_prev_points</name>
         <load_address>0x20201008</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201008</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.bss.g_prev_points</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010c8</run_address>
         <size>0x80</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.bss.g_prev_points_ch1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20201148</run_address>
         <size>0x80</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020100c</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e3">
         <name>.common:gSPI_LCDBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202011c8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1ac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_abbrev</name>
         <load_address>0x1ac</load_address>
         <run_address>0x1ac</run_address>
         <size>0x230</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x3dc</load_address>
         <run_address>0x3dc</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_abbrev</name>
         <load_address>0x449</load_address>
         <run_address>0x449</run_address>
         <size>0x132</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_abbrev</name>
         <load_address>0x57b</load_address>
         <run_address>0x57b</run_address>
         <size>0x130</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_abbrev</name>
         <load_address>0x6ab</load_address>
         <run_address>0x6ab</run_address>
         <size>0x54</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-171">
         <name>.debug_abbrev</name>
         <load_address>0x6ff</load_address>
         <run_address>0x6ff</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_abbrev</name>
         <load_address>0x870</load_address>
         <run_address>0x870</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-183">
         <name>.debug_abbrev</name>
         <load_address>0x8d2</load_address>
         <run_address>0x8d2</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_abbrev</name>
         <load_address>0xa52</load_address>
         <run_address>0xa52</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.debug_abbrev</name>
         <load_address>0xcc9</load_address>
         <run_address>0xcc9</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0xf4f</load_address>
         <run_address>0xf4f</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x1011</load_address>
         <run_address>0x1011</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0x1081</load_address>
         <run_address>0x1081</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0x110e</load_address>
         <run_address>0x110e</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_abbrev</name>
         <load_address>0x11bd</load_address>
         <run_address>0x11bd</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_abbrev</name>
         <load_address>0x1255</load_address>
         <run_address>0x1255</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_abbrev</name>
         <load_address>0x13c5</load_address>
         <run_address>0x13c5</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_abbrev</name>
         <load_address>0x13fe</load_address>
         <run_address>0x13fe</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_abbrev</name>
         <load_address>0x142a</load_address>
         <run_address>0x142a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_abbrev</name>
         <load_address>0x1451</load_address>
         <run_address>0x1451</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_abbrev</name>
         <load_address>0x1478</load_address>
         <run_address>0x1478</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_abbrev</name>
         <load_address>0x149f</load_address>
         <run_address>0x149f</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0x14c6</load_address>
         <run_address>0x14c6</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-182">
         <name>.debug_abbrev</name>
         <load_address>0x14ed</load_address>
         <run_address>0x14ed</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_abbrev</name>
         <load_address>0x1514</load_address>
         <run_address>0x1514</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-186">
         <name>.debug_abbrev</name>
         <load_address>0x153b</load_address>
         <run_address>0x153b</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_abbrev</name>
         <load_address>0x1594</load_address>
         <run_address>0x1594</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.debug_abbrev</name>
         <load_address>0x15b9</load_address>
         <run_address>0x15b9</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x15e1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_info</name>
         <load_address>0x15e1</load_address>
         <run_address>0x15e1</run_address>
         <size>0x3a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x5008</load_address>
         <run_address>0x5008</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_info</name>
         <load_address>0x5088</load_address>
         <run_address>0x5088</run_address>
         <size>0xedf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_info</name>
         <load_address>0x5f67</load_address>
         <run_address>0x5f67</run_address>
         <size>0x10f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_info</name>
         <load_address>0x7057</load_address>
         <run_address>0x7057</run_address>
         <size>0xe2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_info</name>
         <load_address>0x7139</load_address>
         <run_address>0x7139</run_address>
         <size>0x745</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_info</name>
         <load_address>0x787e</load_address>
         <run_address>0x787e</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-175">
         <name>.debug_info</name>
         <load_address>0x78f3</load_address>
         <run_address>0x78f3</run_address>
         <size>0x6ea</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_info</name>
         <load_address>0x7fdd</load_address>
         <run_address>0x7fdd</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_info</name>
         <load_address>0x911f</load_address>
         <run_address>0x911f</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_info</name>
         <load_address>0xc291</load_address>
         <run_address>0xc291</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0xc423</load_address>
         <run_address>0xc423</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xc4e9</load_address>
         <run_address>0xc4e9</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xc665</load_address>
         <run_address>0xc665</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_info</name>
         <load_address>0xca88</load_address>
         <run_address>0xca88</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_info</name>
         <load_address>0xcb80</load_address>
         <run_address>0xcb80</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_info</name>
         <load_address>0xd2c4</load_address>
         <run_address>0xd2c4</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_info</name>
         <load_address>0xd30a</load_address>
         <run_address>0xd30a</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_info</name>
         <load_address>0xd345</load_address>
         <run_address>0xd345</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_info</name>
         <load_address>0xd4da</load_address>
         <run_address>0xd4da</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_info</name>
         <load_address>0xd66f</load_address>
         <run_address>0xd66f</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_info</name>
         <load_address>0xd806</load_address>
         <run_address>0xd806</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0xd9a1</load_address>
         <run_address>0xd9a1</run_address>
         <size>0x1a1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.debug_info</name>
         <load_address>0xdb42</load_address>
         <run_address>0xdb42</run_address>
         <size>0x1c4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_info</name>
         <load_address>0xdd06</load_address>
         <run_address>0xdd06</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_info</name>
         <load_address>0xde9d</load_address>
         <run_address>0xde9d</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_info</name>
         <load_address>0xdf22</load_address>
         <run_address>0xdf22</run_address>
         <size>0x2fe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.debug_info</name>
         <load_address>0xe220</load_address>
         <run_address>0xe220</run_address>
         <size>0xb8</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x58</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_ranges</name>
         <load_address>0x58</load_address>
         <run_address>0x58</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x220</load_address>
         <run_address>0x220</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_ranges</name>
         <load_address>0x238</load_address>
         <run_address>0x238</run_address>
         <size>0x88</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_ranges</name>
         <load_address>0x2c0</load_address>
         <run_address>0x2c0</run_address>
         <size>0x68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_ranges</name>
         <load_address>0x328</load_address>
         <run_address>0x328</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_ranges</name>
         <load_address>0x350</load_address>
         <run_address>0x350</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_ranges</name>
         <load_address>0x368</load_address>
         <run_address>0x368</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_ranges</name>
         <load_address>0x6f8</load_address>
         <run_address>0x6f8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x8d0</load_address>
         <run_address>0x8d0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x8e8</load_address>
         <run_address>0x8e8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x938</load_address>
         <run_address>0x938</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_ranges</name>
         <load_address>0x980</load_address>
         <run_address>0x980</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_ranges</name>
         <load_address>0x998</load_address>
         <run_address>0x998</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_ranges</name>
         <load_address>0x9e0</load_address>
         <run_address>0x9e0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_ranges</name>
         <load_address>0x9f8</load_address>
         <run_address>0x9f8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc86</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_str</name>
         <load_address>0xc86</load_address>
         <run_address>0xc86</run_address>
         <size>0x3089</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x3d0f</load_address>
         <run_address>0x3d0f</run_address>
         <size>0x181</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_str</name>
         <load_address>0x3e90</load_address>
         <run_address>0x3e90</run_address>
         <size>0x3cd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_str</name>
         <load_address>0x425d</load_address>
         <run_address>0x425d</run_address>
         <size>0x61e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_str</name>
         <load_address>0x487b</load_address>
         <run_address>0x487b</run_address>
         <size>0x116</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-172">
         <name>.debug_str</name>
         <load_address>0x4991</load_address>
         <run_address>0x4991</run_address>
         <size>0x63b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_str</name>
         <load_address>0x4fcc</load_address>
         <run_address>0x4fcc</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-184">
         <name>.debug_str</name>
         <load_address>0x5143</load_address>
         <run_address>0x5143</run_address>
         <size>0x654</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_str</name>
         <load_address>0x5797</load_address>
         <run_address>0x5797</run_address>
         <size>0xc45</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_str</name>
         <load_address>0x63dc</load_address>
         <run_address>0x63dc</run_address>
         <size>0x1dd6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_str</name>
         <load_address>0x81b2</load_address>
         <run_address>0x81b2</run_address>
         <size>0x19f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x8351</load_address>
         <run_address>0x8351</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x84bd</load_address>
         <run_address>0x84bd</run_address>
         <size>0x1d9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x8696</load_address>
         <run_address>0x8696</run_address>
         <size>0x229</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_str</name>
         <load_address>0x88bf</load_address>
         <run_address>0x88bf</run_address>
         <size>0x14c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_str</name>
         <load_address>0x8a0b</load_address>
         <run_address>0x8a0b</run_address>
         <size>0x333</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_str</name>
         <load_address>0x8d3e</load_address>
         <run_address>0x8d3e</run_address>
         <size>0xf9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_str</name>
         <load_address>0x8e37</load_address>
         <run_address>0x8e37</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-187">
         <name>.debug_str</name>
         <load_address>0x8f24</load_address>
         <run_address>0x8f24</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_frame</name>
         <load_address>0xec</load_address>
         <run_address>0xec</run_address>
         <size>0x520</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x60c</load_address>
         <run_address>0x60c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_frame</name>
         <load_address>0x63c</load_address>
         <run_address>0x63c</run_address>
         <size>0x1f8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_frame</name>
         <load_address>0x834</load_address>
         <run_address>0x834</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_frame</name>
         <load_address>0x960</load_address>
         <run_address>0x960</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_frame</name>
         <load_address>0x9e0</load_address>
         <run_address>0x9e0</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_frame</name>
         <load_address>0xa2c</load_address>
         <run_address>0xa2c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-176">
         <name>.debug_frame</name>
         <load_address>0xa4c</load_address>
         <run_address>0xa4c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_frame</name>
         <load_address>0xa7c</load_address>
         <run_address>0xa7c</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_frame</name>
         <load_address>0xcb0</load_address>
         <run_address>0xcb0</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_frame</name>
         <load_address>0x10b8</load_address>
         <run_address>0x10b8</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x10f0</load_address>
         <run_address>0x10f0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x1118</load_address>
         <run_address>0x1118</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x1148</load_address>
         <run_address>0x1148</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_frame</name>
         <load_address>0x11d8</load_address>
         <run_address>0x11d8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_frame</name>
         <load_address>0x1208</load_address>
         <run_address>0x1208</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_frame</name>
         <load_address>0x1308</load_address>
         <run_address>0x1308</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_frame</name>
         <load_address>0x1328</load_address>
         <run_address>0x1328</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_frame</name>
         <load_address>0x1348</load_address>
         <run_address>0x1348</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7b6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_line</name>
         <load_address>0x7b6</load_address>
         <run_address>0x7b6</run_address>
         <size>0xf63</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0x1719</load_address>
         <run_address>0x1719</run_address>
         <size>0xcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_line</name>
         <load_address>0x17e5</load_address>
         <run_address>0x17e5</run_address>
         <size>0x1249</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_line</name>
         <load_address>0x2a2e</load_address>
         <run_address>0x2a2e</run_address>
         <size>0x659</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_line</name>
         <load_address>0x3087</load_address>
         <run_address>0x3087</run_address>
         <size>0x91</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_line</name>
         <load_address>0x3118</load_address>
         <run_address>0x3118</run_address>
         <size>0x280</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_line</name>
         <load_address>0x3398</load_address>
         <run_address>0x3398</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_line</name>
         <load_address>0x3511</load_address>
         <run_address>0x3511</run_address>
         <size>0x249</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_line</name>
         <load_address>0x375a</load_address>
         <run_address>0x375a</run_address>
         <size>0xc1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_line</name>
         <load_address>0x4375</load_address>
         <run_address>0x4375</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_line</name>
         <load_address>0x5ae4</load_address>
         <run_address>0x5ae4</run_address>
         <size>0x102</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x5be6</load_address>
         <run_address>0x5be6</run_address>
         <size>0x63</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x5c49</load_address>
         <run_address>0x5c49</run_address>
         <size>0x10e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x5d57</load_address>
         <run_address>0x5d57</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_line</name>
         <load_address>0x5f33</load_address>
         <run_address>0x5f33</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_line</name>
         <load_address>0x5f9a</load_address>
         <run_address>0x5f9a</run_address>
         <size>0x403</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_line</name>
         <load_address>0x639d</load_address>
         <run_address>0x639d</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_line</name>
         <load_address>0x63db</load_address>
         <run_address>0x63db</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_line</name>
         <load_address>0x641c</load_address>
         <run_address>0x641c</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_line</name>
         <load_address>0x6500</load_address>
         <run_address>0x6500</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_line</name>
         <load_address>0x65e0</load_address>
         <run_address>0x65e0</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_line</name>
         <load_address>0x669c</load_address>
         <run_address>0x669c</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x675c</load_address>
         <run_address>0x675c</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.debug_line</name>
         <load_address>0x6804</load_address>
         <run_address>0x6804</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_line</name>
         <load_address>0x68ca</load_address>
         <run_address>0x68ca</run_address>
         <size>0xbd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_line</name>
         <load_address>0x6987</load_address>
         <run_address>0x6987</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_line</name>
         <load_address>0x6a40</load_address>
         <run_address>0x6a40</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
      <object_component id="oc-141">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_loc</name>
         <load_address>0xc7</load_address>
         <run_address>0xc7</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-185">
         <name>.debug_loc</name>
         <load_address>0xda</load_address>
         <run_address>0xda</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_loc</name>
         <load_address>0x1aa</load_address>
         <run_address>0x1aa</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_loc</name>
         <load_address>0x9c0</load_address>
         <run_address>0x9c0</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_loc</name>
         <load_address>0x23e7</load_address>
         <run_address>0x23e7</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x2553</load_address>
         <run_address>0x2553</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x25c2</load_address>
         <run_address>0x25c2</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x2729</load_address>
         <run_address>0x2729</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_loc</name>
         <load_address>0x2801</load_address>
         <run_address>0x2801</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_loc</name>
         <load_address>0x2827</load_address>
         <run_address>0x2827</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-188">
         <name>.debug_loc</name>
         <load_address>0x2c4b</load_address>
         <run_address>0x2c4b</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e4"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e0"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e2"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e3"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e5"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x2148</size>
         <contents>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x28d8</load_address>
         <run_address>0x28d8</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-1c9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x2208</load_address>
         <run_address>0x2208</run_address>
         <size>0x6d0</size>
         <contents>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-132"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-192"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x1009</size>
         <contents>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-b5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x2020100c</run_address>
         <size>0x1e4</size>
         <contents>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-e3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-1cd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-189" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18a" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18b" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18c" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18d" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18e" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-190" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ac" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x15c8</size>
         <contents>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-1cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ae" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe2d8</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-1ce"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b0" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa20</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-7c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b2" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x90bf</size>
         <contents>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-187"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b4" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1378</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-180"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b6" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6ae0</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-7d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b8" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2c6b</size>
         <contents>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-188"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c2" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x108</size>
         <contents>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-7b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1cc" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-1db" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2908</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1dc" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x11f0</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1dd" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x2908</used_space>
         <unused_space>0x1d6f8</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x2148</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2208</start_address>
               <size>0x6d0</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x28d8</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x2908</start_address>
               <size>0x1d6f8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x13ed</used_space>
         <unused_space>0x6c13</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-18e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-190"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1009</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20201009</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x2020100c</start_address>
               <size>0x1e4</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202011f0</start_address>
               <size>0x6c10</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x28e4</load_address>
            <load_size>0x9</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1009</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x28f0</load_address>
            <load_size>0x8</load_size>
            <run_address>0x2020100c</run_address>
            <run_size>0x1e4</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x28f8</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x2908</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x2908</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x28d8</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x28e4</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-63">
         <name>main</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-64">
         <name>gADCSamples</name>
         <value>0x20200000</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-65">
         <name>gADCSamples_ch1</name>
         <value>0x20200800</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-66">
         <name>adc0_done</name>
         <value>0x20201000</value>
         <object_component_ref idref="oc-5d"/>
      </symbol>
      <symbol id="sm-67">
         <name>adc1_done</name>
         <value>0x20201004</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-68">
         <name>ADC0_IRQHandler</name>
         <value>0x1d1d</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-69">
         <name>ADC1_IRQHandler</name>
         <value>0x1d3d</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-143">
         <name>SYSCFG_DL_init</name>
         <value>0x1899</value>
         <object_component_ref idref="oc-83"/>
      </symbol>
      <symbol id="sm-144">
         <name>SYSCFG_DL_initPower</name>
         <value>0x150d</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-145">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x15e9</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-146">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x1a3d</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-147">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x1645</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-148">
         <name>SYSCFG_DL_SPI_LCD_init</name>
         <value>0x1859</value>
         <object_component_ref idref="oc-dd"/>
      </symbol>
      <symbol id="sm-149">
         <name>SYSCFG_DL_ADC12_0_init</name>
         <value>0x11dd</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-14a">
         <name>SYSCFG_DL_ADC12_1_init</name>
         <value>0x113d</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-14b">
         <name>SYSCFG_DL_DMA_init</name>
         <value>0x21cd</value>
         <object_component_ref idref="oc-e0"/>
      </symbol>
      <symbol id="sm-14c">
         <name>SYSCFG_DL_SYSCTL_CLK_init</name>
         <value>0x2071</value>
         <object_component_ref idref="oc-e1"/>
      </symbol>
      <symbol id="sm-14d">
         <name>gTIMER_0Backup</name>
         <value>0x2020100c</value>
      </symbol>
      <symbol id="sm-14e">
         <name>gSPI_LCDBackup</name>
         <value>0x202011c8</value>
      </symbol>
      <symbol id="sm-14f">
         <name>SYSCFG_DL_DMA_CH0_init</name>
         <value>0x1c81</value>
         <object_component_ref idref="oc-154"/>
      </symbol>
      <symbol id="sm-150">
         <name>SYSCFG_DL_DMA_CH1_init</name>
         <value>0x1b89</value>
         <object_component_ref idref="oc-155"/>
      </symbol>
      <symbol id="sm-15b">
         <name>Default_Handler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15c">
         <name>Reset_Handler</name>
         <value>0x21fb</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-15d">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-15e">
         <name>NMI_Handler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15f">
         <name>HardFault_Handler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-160">
         <name>SVC_Handler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-161">
         <name>PendSV_Handler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-162">
         <name>SysTick_Handler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-163">
         <name>GROUP0_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-164">
         <name>GROUP1_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-165">
         <name>TIMG8_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-166">
         <name>UART3_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-167">
         <name>CANFD0_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-168">
         <name>DAC0_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-169">
         <name>SPI0_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-16a">
         <name>SPI1_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-16b">
         <name>UART1_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-16c">
         <name>UART2_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-16d">
         <name>UART0_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-16e">
         <name>TIMG0_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-16f">
         <name>TIMG6_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-170">
         <name>TIMA0_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-171">
         <name>TIMA1_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-172">
         <name>TIMG7_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-173">
         <name>TIMG12_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-174">
         <name>I2C0_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-175">
         <name>I2C1_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-176">
         <name>AES_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-177">
         <name>RTC_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-178">
         <name>DMA_IRQHandler</name>
         <value>0x21f3</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-194">
         <name>LCD_Fill</name>
         <value>0x1305</value>
         <object_component_ref idref="oc-94"/>
      </symbol>
      <symbol id="sm-195">
         <name>LCD_DrawPoint</name>
         <value>0x1b5d</value>
         <object_component_ref idref="oc-f5"/>
      </symbol>
      <symbol id="sm-196">
         <name>LCD_DrawLine</name>
         <value>0xe15</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-197">
         <name>LCD_ShowChinese</name>
         <value>0xf3d</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-198">
         <name>LCD_ShowChinese12x12</name>
         <value>0x6b5</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-199">
         <name>LCD_ShowChinese16x16</name>
         <value>0x88d</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-19a">
         <name>LCD_ShowChinese24x24</name>
         <value>0xa65</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-19b">
         <name>LCD_ShowChinese32x32</name>
         <value>0xc3d</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-19c">
         <name>tfont12</name>
         <value>0x279c</value>
         <object_component_ref idref="oc-15d"/>
      </symbol>
      <symbol id="sm-19d">
         <name>tfont16</name>
         <value>0x2492</value>
         <object_component_ref idref="oc-15e"/>
      </symbol>
      <symbol id="sm-19e">
         <name>tfont24</name>
         <value>0x262a</value>
         <object_component_ref idref="oc-15f"/>
      </symbol>
      <symbol id="sm-19f">
         <name>tfont32</name>
         <value>0x2208</value>
         <object_component_ref idref="oc-160"/>
      </symbol>
      <symbol id="sm-1c7">
         <name>LCD_Writ_Bus</name>
         <value>0x1585</value>
         <object_component_ref idref="oc-157"/>
      </symbol>
      <symbol id="sm-1c8">
         <name>LCD_WR_DATA8</name>
         <value>0x2145</value>
         <object_component_ref idref="oc-ec"/>
      </symbol>
      <symbol id="sm-1c9">
         <name>LCD_WR_DATA</name>
         <value>0x1dbb</value>
         <object_component_ref idref="oc-f0"/>
      </symbol>
      <symbol id="sm-1ca">
         <name>LCD_WR_REG</name>
         <value>0x1b01</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-1cb">
         <name>LCD_Address_Set</name>
         <value>0x169d</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-1cc">
         <name>LCD_Init</name>
         <value>0x455</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-1d5">
         <name>delay_ms</name>
         <value>0x20b5</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-1d6">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d7">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d8">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d9">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1da">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1db">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1dc">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1dd">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1de">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1e9">
         <name>DL_ADC12_setClockConfig</name>
         <value>0x1819</value>
         <object_component_ref idref="oc-140"/>
      </symbol>
      <symbol id="sm-1f2">
         <name>DL_Common_delayCycles</name>
         <value>0x21d9</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-1fc">
         <name>DL_DMA_initChannel</name>
         <value>0x173d</value>
         <object_component_ref idref="oc-174"/>
      </symbol>
      <symbol id="sm-209">
         <name>DL_SPI_init</name>
         <value>0x17d5</value>
         <object_component_ref idref="oc-13a"/>
      </symbol>
      <symbol id="sm-20a">
         <name>DL_SPI_setClockConfig</name>
         <value>0x216b</value>
         <object_component_ref idref="oc-134"/>
      </symbol>
      <symbol id="sm-21a">
         <name>DL_Timer_setClockConfig</name>
         <value>0x1eb9</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-21b">
         <name>DL_Timer_initTimerMode</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-12e"/>
      </symbol>
      <symbol id="sm-225">
         <name>__TI_zero_init_nomemset</name>
         <value>0x20cb</value>
         <object_component_ref idref="oc-4c"/>
      </symbol>
      <symbol id="sm-22e">
         <name>__TI_decompress_none</name>
         <value>0x218f</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-239">
         <name>__TI_decompress_lzss</name>
         <value>0x1491</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-244">
         <name>_c_int00_noargs</name>
         <value>0x1ca9</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-245">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-250">
         <name>abort</name>
         <value>0x21ed</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-25d">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1955</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-265">
         <name>_system_pre_init</name>
         <value>0x21ff</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-26e">
         <name>HOSTexit</name>
         <value>0x21f7</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-26f">
         <name>C$$EXIT</name>
         <value>0x21f6</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-275">
         <name>__aeabi_fmul</name>
         <value>0x1279</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-276">
         <name>__mulsf3</name>
         <value>0x1279</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-27c">
         <name>__aeabi_fdiv</name>
         <value>0x140d</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-27d">
         <name>__divsf3</name>
         <value>0x140d</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-283">
         <name>__aeabi_f2iz</name>
         <value>0x1a05</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-284">
         <name>__fixsfsi</name>
         <value>0x1a05</value>
         <object_component_ref idref="oc-ae"/>
      </symbol>
      <symbol id="sm-28a">
         <name>__aeabi_i2f</name>
         <value>0x1919</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-28b">
         <name>__floatsisf</name>
         <value>0x1919</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-291">
         <name>__aeabi_memcpy</name>
         <value>0x21e5</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-292">
         <name>__aeabi_memcpy4</name>
         <value>0x21e5</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-293">
         <name>__aeabi_memcpy8</name>
         <value>0x21e5</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-299">
         <name>__aeabi_uidiv</name>
         <value>0x18d9</value>
         <object_component_ref idref="oc-159"/>
      </symbol>
      <symbol id="sm-29a">
         <name>__aeabi_uidivmod</name>
         <value>0x18d9</value>
         <object_component_ref idref="oc-159"/>
      </symbol>
      <symbol id="sm-2a0">
         <name>__muldsi3</name>
         <value>0x1991</value>
         <object_component_ref idref="oc-f8"/>
      </symbol>
      <symbol id="sm-2aa">
         <name>__aeabi_idiv0</name>
         <value>0x1053</value>
         <object_component_ref idref="oc-17d"/>
      </symbol>
      <symbol id="sm-2b3">
         <name>TI_memcpy_small</name>
         <value>0x217d</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-2b4">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2b7">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2b8">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
